"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue-fields.tsx":
/*!***********************************************************!*\
  !*** ./src/app/ui/logbook/forms/vessel-rescue-fields.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VesselRescueFields; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _app_offline_models_eventType_VesselRescue__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/eventType_VesselRescue */ \"(app-pages-browser)/./src/app/offline/models/eventType_VesselRescue.js\");\n/* harmony import */ var _app_offline_models_cgEventMission__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/cgEventMission */ \"(app-pages-browser)/./src/app/offline/models/cgEventMission.js\");\n/* harmony import */ var _app_offline_models_missionTimeline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/missionTimeline */ \"(app-pages-browser)/./src/app/offline/models/missionTimeline.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/app/offline/models/logBookEntry */ \"(app-pages-browser)/./src/app/offline/models/logBookEntry.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _app_ui_maintenance_task_task__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/app/ui/maintenance/task/task */ \"(app-pages-browser)/./src/app/ui/maintenance/task/task.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction VesselRescueFields(param) {\n    let { geoLocations, selectedEvent = false, closeModal, handleSaveParent, currentRescueID, type, eventCurrentLocation, locationDescription, setLocationDescription, offline = false, locked = false, showSaveButtons = true, onSaveFunctionReady } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"HH:mm\"));\n    const [openCommentsDialog, setOpenCommentsDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [commentTime, setCommentTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [rescueData, setRescueData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [missionData, setMissionData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [commentData, setCommentData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [timeline, setTimeline] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [deleteCommentsDialog, setDeleteCommentsDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [allVesselCrews, setAllVesselCrews] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [vessel, setVessel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [masterID, setMasterID] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [crewMemberOptions, setCrewMemberOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [allMembers, setAllMembers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [crewMembers, setCrewMembers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [crewMembersList, setCrewMembersList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [currentMissionLocation, setCurrentMissionLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [showInputDetailsP, setShowInputDetailsPanel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_28__.useMediaQuery)(\"(min-width: 640px)\");\n    const memberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const vesselRescueModel = new _app_offline_models_eventType_VesselRescue__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\n    const cgEventMissionModel = new _app_offline_models_cgEventMission__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const missionTimelineModel = new _app_offline_models_missionTimeline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const cmlbsModel = new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_24__[\"default\"]();\n    const logbookModel = new _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_25__[\"default\"]();\n    const handleTimeChange = (date)=>{\n        setTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    };\n    var _searchParams_get;\n    const vesselID = (_searchParams_get = searchParams.get(\"vesselID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    var _searchParams_get1;\n    const logentryID = (_searchParams_get1 = searchParams.get(\"logentryID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setRescueData(false);\n        if (currentRescueID) {\n            getCurrentEvent(currentRescueID);\n        }\n    }, [\n        currentRescueID\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setCurrentLocation(eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.currentLocation);\n        handleLocationChange({\n            value: eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.geoLocationID\n        });\n    }, [\n        eventCurrentLocation\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (rescueData) {\n            setRescueData((prev)=>{\n                return {\n                    ...prev,\n                    locationDescription\n                };\n            });\n        }\n    }, [\n        locationDescription\n    ]);\n    const getCurrentEvent = async (currentRescueID)=>{\n        if (currentRescueID > 0) {\n            if (offline) {\n                const event = await vesselRescueModel.getById(currentRescueID);\n                if (event) {\n                    var _event_mission, _event_mission1, _event_mission2, _event_mission_missionType, _event_mission3, _event_mission4, _event_mission_operationOutcome, _event_mission5, _event_mission_currentLocation, _event_mission6, _event_mission7, _event_mission_currentLocation1, _event_mission8, _event_mission_currentLocation2, _event_mission9, _event_missionTimeline, _event_mission_currentLocation3, _event_mission10, _event_mission_currentLocation4, _event_mission11;\n                    setRescueData({\n                        vesselName: (event === null || event === void 0 ? void 0 : event.vesselName) ? event === null || event === void 0 ? void 0 : event.vesselName : \"\",\n                        callSign: (event === null || event === void 0 ? void 0 : event.callSign) ? event === null || event === void 0 ? void 0 : event.callSign : \"\",\n                        pob: (event === null || event === void 0 ? void 0 : event.pob) ? event === null || event === void 0 ? void 0 : event.pob : \"\",\n                        latitude: (event === null || event === void 0 ? void 0 : event.latitude) ? event === null || event === void 0 ? void 0 : event.latitude : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.latitude,\n                        longitude: (event === null || event === void 0 ? void 0 : event.longitude) ? event === null || event === void 0 ? void 0 : event.longitude : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.longitude,\n                        locationDescription: (event === null || event === void 0 ? void 0 : event.locationDescription) ? event === null || event === void 0 ? void 0 : event.locationDescription : \"\",\n                        vesselLength: (event === null || event === void 0 ? void 0 : event.vesselLength) ? event === null || event === void 0 ? void 0 : event.vesselLength : \"\",\n                        vesselType: (event === null || event === void 0 ? void 0 : event.vesselType) ? event === null || event === void 0 ? void 0 : event.vesselType : \"\",\n                        makeAndModel: (event === null || event === void 0 ? void 0 : event.makeAndModel) ? event === null || event === void 0 ? void 0 : event.makeAndModel : \"\",\n                        color: (event === null || event === void 0 ? void 0 : event.color) ? event === null || event === void 0 ? void 0 : event.color : \"\",\n                        ownerName: (event === null || event === void 0 ? void 0 : event.ownerName) ? event === null || event === void 0 ? void 0 : event.ownerName : \"\",\n                        phone: (event === null || event === void 0 ? void 0 : event.phone) ? event === null || event === void 0 ? void 0 : event.phone : \"\",\n                        email: (event === null || event === void 0 ? void 0 : event.email) ? event === null || event === void 0 ? void 0 : event.email : \"\",\n                        address: (event === null || event === void 0 ? void 0 : event.address) ? event === null || event === void 0 ? void 0 : event.address : \"\",\n                        ownerOnBoard: (event === null || event === void 0 ? void 0 : event.ownerOnBoard) ? event === null || event === void 0 ? void 0 : event.ownerOnBoard : false,\n                        cgMembership: (event === null || event === void 0 ? void 0 : event.cgMembership) ? event === null || event === void 0 ? void 0 : event.cgMembership : \"\",\n                        locationID: (event === null || event === void 0 ? void 0 : event.vesselLocationID) ? event === null || event === void 0 ? void 0 : event.vesselLocationID : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.geoLocationID,\n                        missionID: (event === null || event === void 0 ? void 0 : (_event_mission = event.mission) === null || _event_mission === void 0 ? void 0 : _event_mission.id) ? event === null || event === void 0 ? void 0 : (_event_mission1 = event.mission) === null || _event_mission1 === void 0 ? void 0 : _event_mission1.id : \"\",\n                        operationType: (event === null || event === void 0 ? void 0 : event.operationType) ? operationType.filter((operation)=>event === null || event === void 0 ? void 0 : event.operationType.split(\",\").includes(operation.value)) : [],\n                        operationDescription: (event === null || event === void 0 ? void 0 : event.operationDescription) ? event === null || event === void 0 ? void 0 : event.operationDescription : \"\",\n                        vesselTypeDescription: (event === null || event === void 0 ? void 0 : event.vesselTypeDescription) ? event === null || event === void 0 ? void 0 : event.vesselTypeDescription : \"\"\n                    });\n                    setTime(event === null || event === void 0 ? void 0 : (_event_mission2 = event.mission) === null || _event_mission2 === void 0 ? void 0 : _event_mission2.completedAt);\n                    setMissionData({\n                        missionType: event === null || event === void 0 ? void 0 : (_event_mission3 = event.mission) === null || _event_mission3 === void 0 ? void 0 : (_event_mission_missionType = _event_mission3.missionType) === null || _event_mission_missionType === void 0 ? void 0 : _event_mission_missionType.replaceAll(\"_\", \" \"),\n                        description: event === null || event === void 0 ? void 0 : (_event_mission4 = event.mission) === null || _event_mission4 === void 0 ? void 0 : _event_mission4.description,\n                        operationOutcome: event === null || event === void 0 ? void 0 : (_event_mission5 = event.mission) === null || _event_mission5 === void 0 ? void 0 : (_event_mission_operationOutcome = _event_mission5.operationOutcome) === null || _event_mission_operationOutcome === void 0 ? void 0 : _event_mission_operationOutcome.replaceAll(\"_\", \" \"),\n                        currentLocationID: event === null || event === void 0 ? void 0 : (_event_mission6 = event.mission) === null || _event_mission6 === void 0 ? void 0 : (_event_mission_currentLocation = _event_mission6.currentLocation) === null || _event_mission_currentLocation === void 0 ? void 0 : _event_mission_currentLocation.id,\n                        operationDescription: event === null || event === void 0 ? void 0 : (_event_mission7 = event.mission) === null || _event_mission7 === void 0 ? void 0 : _event_mission7.operationDescription,\n                        lat: event === null || event === void 0 ? void 0 : (_event_mission8 = event.mission) === null || _event_mission8 === void 0 ? void 0 : (_event_mission_currentLocation1 = _event_mission8.currentLocation) === null || _event_mission_currentLocation1 === void 0 ? void 0 : _event_mission_currentLocation1.lat,\n                        long: event === null || event === void 0 ? void 0 : (_event_mission9 = event.mission) === null || _event_mission9 === void 0 ? void 0 : (_event_mission_currentLocation2 = _event_mission9.currentLocation) === null || _event_mission_currentLocation2 === void 0 ? void 0 : _event_mission_currentLocation2.long\n                    });\n                    setTimeline(event === null || event === void 0 ? void 0 : (_event_missionTimeline = event.missionTimeline) === null || _event_missionTimeline === void 0 ? void 0 : _event_missionTimeline.nodes);\n                    setCurrentLocation({\n                        latitude: event === null || event === void 0 ? void 0 : (_event_mission10 = event.mission) === null || _event_mission10 === void 0 ? void 0 : (_event_mission_currentLocation3 = _event_mission10.currentLocation) === null || _event_mission_currentLocation3 === void 0 ? void 0 : _event_mission_currentLocation3.lat,\n                        longitude: event === null || event === void 0 ? void 0 : (_event_mission11 = event.mission) === null || _event_mission11 === void 0 ? void 0 : (_event_mission_currentLocation4 = _event_mission11.currentLocation) === null || _event_mission_currentLocation4 === void 0 ? void 0 : _event_mission_currentLocation4.long\n                    });\n                    setCurrentMissionLocation({\n                        latitude: event === null || event === void 0 ? void 0 : event.lat,\n                        longitude: event === null || event === void 0 ? void 0 : event.long\n                    });\n                    setLocationDescription(event === null || event === void 0 ? void 0 : event.locationDescription);\n                }\n            } else {\n                getTripEvent({\n                    variables: {\n                        id: +currentRescueID\n                    }\n                });\n            }\n        }\n    };\n    const [getTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetTripEvent_VesselRescue, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneEventType_VesselRescue;\n            if (event) {\n                var _event_mission, _event_mission1, _event_mission2, _event_mission_missionType, _event_mission3, _event_mission4, _event_mission_operationOutcome, _event_mission5, _event_mission_currentLocation, _event_mission6, _event_mission7, _event_mission_currentLocation1, _event_mission8, _event_mission_currentLocation2, _event_mission9, _event_missionTimeline, _event_mission_currentLocation3, _event_mission10, _event_mission_currentLocation4, _event_mission11;\n                setRescueData({\n                    vesselName: (event === null || event === void 0 ? void 0 : event.vesselName) ? event === null || event === void 0 ? void 0 : event.vesselName : \"\",\n                    callSign: (event === null || event === void 0 ? void 0 : event.callSign) ? event === null || event === void 0 ? void 0 : event.callSign : \"\",\n                    pob: (event === null || event === void 0 ? void 0 : event.pob) ? event === null || event === void 0 ? void 0 : event.pob : \"\",\n                    latitude: (event === null || event === void 0 ? void 0 : event.latitude) ? event === null || event === void 0 ? void 0 : event.latitude : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.latitude,\n                    longitude: (event === null || event === void 0 ? void 0 : event.longitude) ? event === null || event === void 0 ? void 0 : event.longitude : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.longitude,\n                    locationDescription: (event === null || event === void 0 ? void 0 : event.locationDescription) ? event === null || event === void 0 ? void 0 : event.locationDescription : \"\",\n                    vesselLength: (event === null || event === void 0 ? void 0 : event.vesselLength) ? event === null || event === void 0 ? void 0 : event.vesselLength : \"\",\n                    vesselType: (event === null || event === void 0 ? void 0 : event.vesselType) ? event === null || event === void 0 ? void 0 : event.vesselType : \"\",\n                    makeAndModel: (event === null || event === void 0 ? void 0 : event.makeAndModel) ? event === null || event === void 0 ? void 0 : event.makeAndModel : \"\",\n                    color: (event === null || event === void 0 ? void 0 : event.color) ? event === null || event === void 0 ? void 0 : event.color : \"\",\n                    ownerName: (event === null || event === void 0 ? void 0 : event.ownerName) ? event === null || event === void 0 ? void 0 : event.ownerName : \"\",\n                    phone: (event === null || event === void 0 ? void 0 : event.phone) ? event === null || event === void 0 ? void 0 : event.phone : \"\",\n                    email: (event === null || event === void 0 ? void 0 : event.email) ? event === null || event === void 0 ? void 0 : event.email : \"\",\n                    address: (event === null || event === void 0 ? void 0 : event.address) ? event === null || event === void 0 ? void 0 : event.address : \"\",\n                    ownerOnBoard: (event === null || event === void 0 ? void 0 : event.ownerOnBoard) ? event === null || event === void 0 ? void 0 : event.ownerOnBoard : false,\n                    cgMembership: (event === null || event === void 0 ? void 0 : event.cgMembership) ? event === null || event === void 0 ? void 0 : event.cgMembership : \"\",\n                    locationID: (event === null || event === void 0 ? void 0 : event.vesselLocationID) ? event === null || event === void 0 ? void 0 : event.vesselLocationID : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.geoLocationID,\n                    missionID: (event === null || event === void 0 ? void 0 : (_event_mission = event.mission) === null || _event_mission === void 0 ? void 0 : _event_mission.id) ? event === null || event === void 0 ? void 0 : (_event_mission1 = event.mission) === null || _event_mission1 === void 0 ? void 0 : _event_mission1.id : \"\",\n                    operationType: (event === null || event === void 0 ? void 0 : event.operationType) ? operationType.filter((operation)=>event === null || event === void 0 ? void 0 : event.operationType.split(\",\").includes(operation.value)) : [],\n                    operationDescription: (event === null || event === void 0 ? void 0 : event.operationDescription) ? event === null || event === void 0 ? void 0 : event.operationDescription : \"\",\n                    vesselTypeDescription: (event === null || event === void 0 ? void 0 : event.vesselTypeDescription) ? event === null || event === void 0 ? void 0 : event.vesselTypeDescription : \"\"\n                });\n                setTime(event === null || event === void 0 ? void 0 : (_event_mission2 = event.mission) === null || _event_mission2 === void 0 ? void 0 : _event_mission2.completedAt);\n                setMissionData({\n                    missionType: event === null || event === void 0 ? void 0 : (_event_mission3 = event.mission) === null || _event_mission3 === void 0 ? void 0 : (_event_mission_missionType = _event_mission3.missionType) === null || _event_mission_missionType === void 0 ? void 0 : _event_mission_missionType.replaceAll(\"_\", \" \"),\n                    description: event === null || event === void 0 ? void 0 : (_event_mission4 = event.mission) === null || _event_mission4 === void 0 ? void 0 : _event_mission4.description,\n                    operationOutcome: event === null || event === void 0 ? void 0 : (_event_mission5 = event.mission) === null || _event_mission5 === void 0 ? void 0 : (_event_mission_operationOutcome = _event_mission5.operationOutcome) === null || _event_mission_operationOutcome === void 0 ? void 0 : _event_mission_operationOutcome.replaceAll(\"_\", \" \"),\n                    currentLocationID: event === null || event === void 0 ? void 0 : (_event_mission6 = event.mission) === null || _event_mission6 === void 0 ? void 0 : (_event_mission_currentLocation = _event_mission6.currentLocation) === null || _event_mission_currentLocation === void 0 ? void 0 : _event_mission_currentLocation.id,\n                    operationDescription: event === null || event === void 0 ? void 0 : (_event_mission7 = event.mission) === null || _event_mission7 === void 0 ? void 0 : _event_mission7.operationDescription,\n                    lat: event === null || event === void 0 ? void 0 : (_event_mission8 = event.mission) === null || _event_mission8 === void 0 ? void 0 : (_event_mission_currentLocation1 = _event_mission8.currentLocation) === null || _event_mission_currentLocation1 === void 0 ? void 0 : _event_mission_currentLocation1.lat,\n                    long: event === null || event === void 0 ? void 0 : (_event_mission9 = event.mission) === null || _event_mission9 === void 0 ? void 0 : (_event_mission_currentLocation2 = _event_mission9.currentLocation) === null || _event_mission_currentLocation2 === void 0 ? void 0 : _event_mission_currentLocation2.long\n                });\n                setTimeline(event === null || event === void 0 ? void 0 : (_event_missionTimeline = event.missionTimeline) === null || _event_missionTimeline === void 0 ? void 0 : _event_missionTimeline.nodes);\n                setCurrentLocation({\n                    latitude: event === null || event === void 0 ? void 0 : (_event_mission10 = event.mission) === null || _event_mission10 === void 0 ? void 0 : (_event_mission_currentLocation3 = _event_mission10.currentLocation) === null || _event_mission_currentLocation3 === void 0 ? void 0 : _event_mission_currentLocation3.lat,\n                    longitude: event === null || event === void 0 ? void 0 : (_event_mission11 = event.mission) === null || _event_mission11 === void 0 ? void 0 : (_event_mission_currentLocation4 = _event_mission11.currentLocation) === null || _event_mission_currentLocation4 === void 0 ? void 0 : _event_mission_currentLocation4.long\n                });\n                setCurrentMissionLocation({\n                    latitude: event === null || event === void 0 ? void 0 : event.lat,\n                    longitude: event === null || event === void 0 ? void 0 : event.long\n                });\n                setLocationDescription(event === null || event === void 0 ? void 0 : event.locationDescription);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    const handleSetMemberList = (members)=>{\n        var _members_filter;\n        setMembers(members === null || members === void 0 ? void 0 : (_members_filter = members.filter((member)=>member.archived == false && member.firstName != \"\")) === null || _members_filter === void 0 ? void 0 : _members_filter.map((member)=>({\n                label: member.firstName + \" \" + member.surname,\n                value: member.id\n            })));\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_6__.getSeaLogsMembersList)(handleSetMemberList, offline);\n    const handleCommentTimeChange = (date)=>{\n        setCommentTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    // setCommentTime(date)\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (geoLocations) {\n            setLocations(geoLocations.map((location)=>({\n                    label: location.title,\n                    value: location.id,\n                    latitude: location.lat,\n                    longitude: location.long\n                })));\n        }\n    }, [\n        geoLocations\n    ]);\n    const vesselTypes = [\n        {\n            label: \"Commercial\",\n            value: \"Commercial\"\n        },\n        {\n            label: \"Recreation\",\n            value: \"Recreation\"\n        },\n        // { label: 'Power', value: 'Power' },\n        {\n            label: \"Sail\",\n            value: \"Sail\"\n        },\n        {\n            label: \"Paddle crafts\",\n            value: \"Paddle crafts\"\n        },\n        {\n            label: \"PWC\",\n            value: \"PWC\"\n        },\n        {\n            label: \"Other\",\n            value: \"Other\"\n        }\n    ];\n    const missions = [\n        {\n            label: \"To locate\",\n            value: \"To locate\"\n        },\n        {\n            label: \"To assist\",\n            value: \"To assist\"\n        },\n        {\n            label: \"To save\",\n            value: \"To save\"\n        },\n        {\n            label: \"To rescue\",\n            value: \"To rescue\"\n        },\n        {\n            label: \"To remove\",\n            value: \"To remove\"\n        }\n    ];\n    const operationOutcomes = [\n        {\n            label: \"Assisted by others\",\n            value: \"Assisted by others\"\n        },\n        {\n            label: \"Assisted on scene\",\n            value: \"Assisted on scene\"\n        },\n        {\n            label: \"Medical treatment\",\n            value: \"Medical treatment\"\n        },\n        {\n            label: \"Safe and well\",\n            value: \"Safe and well\"\n        },\n        {\n            label: \"Not located\",\n            value: \"Not located\"\n        },\n        {\n            label: \"Not recoverable\",\n            value: \"Not recoverable\"\n        },\n        {\n            label: \"Fatality\",\n            value: \"Fatality\"\n        },\n        {\n            label: \"Stood down\",\n            value: \"Stood down\"\n        },\n        {\n            label: \"Other\",\n            value: \"Other\"\n        }\n    ];\n    const commentTypes = [\n        {\n            label: \"General\",\n            value: \"General\"\n        },\n        {\n            label: \"Underway\",\n            value: \"Underway\"\n        },\n        {\n            label: \"On Scene\",\n            value: \"On Scene\"\n        }\n    ];\n    const operationType = [\n        {\n            label: \"Mechanical / equipment failure\",\n            value: \"Mechanical / equipment failure\"\n        },\n        {\n            label: \"Vessel adrift\",\n            value: \"Vessel adrift\"\n        },\n        {\n            label: \"Vessel aground\",\n            value: \"Vessel aground\"\n        },\n        {\n            label: \"Capsize\",\n            value: \"Capsize\"\n        },\n        {\n            label: \"Vessel requiring tow\",\n            value: \"Vessel requiring tow\"\n        },\n        {\n            label: \"Flare sighting\",\n            value: \"Flare sighting\"\n        },\n        {\n            label: \"Vessel sinking\",\n            value: \"Vessel sinking\"\n        },\n        {\n            label: \"Collision\",\n            value: \"Collision\"\n        },\n        {\n            label: \"Vessel overdue\",\n            value: \"Vessel overdue\"\n        },\n        {\n            label: \"Other\",\n            value: \"Other\"\n        }\n    ];\n    const handleSaveComments = async ()=>{\n        if ((rescueData === null || rescueData === void 0 ? void 0 : rescueData.missionID) === undefined) {\n            sonner__WEBPACK_IMPORTED_MODULE_19__.toast.error(\"Please save the event first in order to create timeline!\");\n            setOpenCommentsDialog(false);\n            return;\n        }\n        const variables = {\n            input: {\n                commentType: (commentData === null || commentData === void 0 ? void 0 : commentData.commentType) ? commentData === null || commentData === void 0 ? void 0 : commentData.commentType : \"General\",\n                description: content ? content : \"\",\n                time: commentTime ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"DD/MM/YYYY\") + \" \" + commentTime : dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"DD/MM/YYYY HH:mm\"),\n                authorID: commentData === null || commentData === void 0 ? void 0 : commentData.authorID,\n                // missionID: rescueData?.missionID,\n                vesselRescueID: currentRescueID\n            }\n        };\n        if ((commentData === null || commentData === void 0 ? void 0 : commentData.id) > 0) {\n            if (offline) {\n                await missionTimelineModel.save({\n                    id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                    ...variables.input\n                });\n                // toast.success('Mission timeline updated')\n                setOpenCommentsDialog(false);\n                setDeleteCommentsDialog(false);\n                getCurrentEvent(currentRescueID);\n            } else {\n                updateMissionTimeline({\n                    variables: {\n                        input: {\n                            id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                await missionTimelineModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__.generateUniqueId)(),\n                    ...variables.input\n                });\n                // toast.success('Mission timeline created')\n                setOpenCommentsDialog(false);\n                setDeleteCommentsDialog(false);\n                await getCurrentEvent(currentRescueID);\n            } else {\n                createMissionTimeline({\n                    variables: {\n                        input: {\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        }\n        setOpenCommentsDialog(false);\n    };\n    const [createMissionTimeline] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateMissionTimeline, {\n        onCompleted: (response)=>{\n            // toast.success('Mission timeline created')\n            setOpenCommentsDialog(false);\n            setDeleteCommentsDialog(false);\n            getCurrentEvent(currentRescueID);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating mission timeline\", error);\n        }\n    });\n    const [updateMissionTimeline] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateMissionTimeline, {\n        onCompleted: (response)=>{\n            // toast.success('Mission timeline updated')\n            setOpenCommentsDialog(false);\n            setDeleteCommentsDialog(false);\n            getCurrentEvent(currentRescueID);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating mission timeline\", error);\n        }\n    });\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const handleSave = async ()=>{\n        var _rescueData_latitude, _currentLocation_latitude, _rescueData_longitude, _currentLocation_longitude, _rescueData_operationType;\n        const variables = {\n            input: {\n                vesselName: rescueData.vesselName,\n                callSign: rescueData.callSign,\n                pob: +rescueData.pob,\n                latitude: rescueData.latitude > 0 ? (_rescueData_latitude = rescueData.latitude) === null || _rescueData_latitude === void 0 ? void 0 : _rescueData_latitude.toString() : (_currentLocation_latitude = currentLocation.latitude) === null || _currentLocation_latitude === void 0 ? void 0 : _currentLocation_latitude.toString(),\n                longitude: rescueData.longitude > 0 ? (_rescueData_longitude = rescueData.longitude) === null || _rescueData_longitude === void 0 ? void 0 : _rescueData_longitude.toString() : (_currentLocation_longitude = currentLocation.longitude) === null || _currentLocation_longitude === void 0 ? void 0 : _currentLocation_longitude.toString(),\n                locationDescription: rescueData.locationDescription,\n                vesselLength: +rescueData.vesselLength,\n                vesselType: rescueData.vesselType,\n                makeAndModel: rescueData.makeAndModel,\n                color: rescueData.color,\n                ownerName: rescueData.ownerName,\n                phone: rescueData.phone,\n                email: rescueData.email,\n                address: rescueData.address,\n                ownerOnBoard: rescueData.ownerOnBoard,\n                cgMembershipType: \"cgnz\",\n                cgMembership: rescueData.cgMembership,\n                missionID: rescueData.missionID,\n                vesselLocationID: rescueData.locationID > 0 ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                operationType: (_rescueData_operationType = rescueData.operationType) === null || _rescueData_operationType === void 0 ? void 0 : _rescueData_operationType.map((type)=>type.value).join(\",\"),\n                operationDescription: rescueData.operationDescription,\n                vesselTypeDescription: rescueData.vesselTypeDescription\n            }\n        };\n        if (currentRescueID > 0) {\n            if (offline) {\n                const data = await vesselRescueModel.save({\n                    id: +currentRescueID,\n                    ...variables.input\n                });\n                if (rescueData.missionID > 0) {\n                    var _currentMissionLocation_latitude, _currentMissionLocation_longitude;\n                    await cgEventMissionModel.save({\n                        id: rescueData.missionID,\n                        missionType: missionData.missionType,\n                        description: missionData.description,\n                        operationDescription: missionData.operationDescription,\n                        operationOutcome: missionData.operationOutcome,\n                        completedAt: time,\n                        currentLocationID: rescueData.locationID,\n                        eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                        eventType: \"VesselRescue\",\n                        lat: (_currentMissionLocation_latitude = currentMissionLocation.latitude) === null || _currentMissionLocation_latitude === void 0 ? void 0 : _currentMissionLocation_latitude.toString(),\n                        long: (_currentMissionLocation_longitude = currentMissionLocation.longitude) === null || _currentMissionLocation_longitude === void 0 ? void 0 : _currentMissionLocation_longitude.toString()\n                    });\n                } else {\n                    var _currentMissionLocation_latitude1, _currentMissionLocation_longitude1;\n                    var _currentMissionLocation_latitude_toString, _currentMissionLocation_longitude_toString;\n                    await cgEventMissionModel.save({\n                        id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__.generateUniqueId)(),\n                        missionType: missionData.missionType,\n                        description: missionData.description,\n                        operationDescription: missionData.operationDescription,\n                        operationOutcome: missionData.operationOutcome,\n                        completedAt: time,\n                        currentLocationID: rescueData.currentLocationID,\n                        eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                        eventType: \"VesselRescue\",\n                        lat: (_currentMissionLocation_latitude_toString = (_currentMissionLocation_latitude1 = currentMissionLocation.latitude) === null || _currentMissionLocation_latitude1 === void 0 ? void 0 : _currentMissionLocation_latitude1.toString()) !== null && _currentMissionLocation_latitude_toString !== void 0 ? _currentMissionLocation_latitude_toString : null,\n                        long: (_currentMissionLocation_longitude_toString = (_currentMissionLocation_longitude1 = currentMissionLocation.longitude) === null || _currentMissionLocation_longitude1 === void 0 ? void 0 : _currentMissionLocation_longitude1.toString()) !== null && _currentMissionLocation_longitude_toString !== void 0 ? _currentMissionLocation_longitude_toString : null\n                    });\n                }\n                handleSaveParent(+currentRescueID, 0);\n            } else {\n                updateEventType_VesselRescue({\n                    variables: {\n                        input: {\n                            id: +currentRescueID,\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                var _rescueData_latitude1, _currentLocation_latitude1, _rescueData_longitude1, _currentLocation_longitude1, _rescueData_operationType1;\n                const data = await vesselRescueModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__.generateUniqueId)(),\n                    vesselName: rescueData.vesselName,\n                    callSign: rescueData.callSign,\n                    pob: +rescueData.pob,\n                    latitude: rescueData.latitude > 0 ? (_rescueData_latitude1 = rescueData.latitude) === null || _rescueData_latitude1 === void 0 ? void 0 : _rescueData_latitude1.toString() : (_currentLocation_latitude1 = currentLocation.latitude) === null || _currentLocation_latitude1 === void 0 ? void 0 : _currentLocation_latitude1.toString(),\n                    longitude: rescueData.longitude > 0 ? (_rescueData_longitude1 = rescueData.longitude) === null || _rescueData_longitude1 === void 0 ? void 0 : _rescueData_longitude1.toString() : (_currentLocation_longitude1 = currentLocation.longitude) === null || _currentLocation_longitude1 === void 0 ? void 0 : _currentLocation_longitude1.toString(),\n                    locationDescription: rescueData.locationDescription,\n                    vesselLength: +rescueData.vesselLength,\n                    vesselType: rescueData.vesselType,\n                    makeAndModel: rescueData.makeAndModel,\n                    color: rescueData.color,\n                    ownerName: rescueData.ownerName,\n                    phone: rescueData.phone,\n                    email: rescueData.email,\n                    address: rescueData.address,\n                    ownerOnBoard: rescueData.ownerOnBoard,\n                    cgMembershipType: \"cgnz\",\n                    cgMembership: rescueData.cgMembership,\n                    missionID: rescueData.missionID,\n                    vesselLocationID: rescueData.locationID ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                    operationType: (_rescueData_operationType1 = rescueData.operationType) === null || _rescueData_operationType1 === void 0 ? void 0 : _rescueData_operationType1.map((type)=>type.value).join(\",\"),\n                    operationDescription: rescueData.operationDescription,\n                    vesselTypeDescription: rescueData.vesselTypeDescription\n                });\n                await cgEventMissionModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__.generateUniqueId)(),\n                    missionType: missionData.missionType,\n                    description: missionData.description,\n                    operationDescription: missionData.operationDescription,\n                    operationOutcome: missionData.operationOutcome,\n                    completedAt: time,\n                    currentLocationID: rescueData.locationID ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                    eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                    eventType: \"VesselRescue\"\n                });\n                handleSaveParent(+(data === null || data === void 0 ? void 0 : data.id), 0);\n                closeModal();\n            } else {\n                var _rescueData_latitude2, _currentLocation_latitude2, _rescueData_longitude2, _currentLocation_longitude2, _rescueData_operationType2;\n                createEventType_VesselRescue({\n                    variables: {\n                        input: {\n                            vesselName: rescueData.vesselName,\n                            callSign: rescueData.callSign,\n                            pob: +rescueData.pob,\n                            latitude: rescueData.latitude > 0 ? (_rescueData_latitude2 = rescueData.latitude) === null || _rescueData_latitude2 === void 0 ? void 0 : _rescueData_latitude2.toString() : (_currentLocation_latitude2 = currentLocation.latitude) === null || _currentLocation_latitude2 === void 0 ? void 0 : _currentLocation_latitude2.toString(),\n                            longitude: rescueData.longitude > 0 ? (_rescueData_longitude2 = rescueData.longitude) === null || _rescueData_longitude2 === void 0 ? void 0 : _rescueData_longitude2.toString() : (_currentLocation_longitude2 = currentLocation.longitude) === null || _currentLocation_longitude2 === void 0 ? void 0 : _currentLocation_longitude2.toString(),\n                            locationDescription: rescueData.locationDescription,\n                            vesselLength: +rescueData.vesselLength,\n                            vesselType: rescueData.vesselType,\n                            makeAndModel: rescueData.makeAndModel,\n                            color: rescueData.color,\n                            ownerName: rescueData.ownerName,\n                            phone: rescueData.phone,\n                            email: rescueData.email,\n                            address: rescueData.address,\n                            ownerOnBoard: rescueData.ownerOnBoard,\n                            cgMembershipType: \"cgnz\",\n                            cgMembership: rescueData.cgMembership,\n                            missionID: rescueData.missionID,\n                            vesselLocationID: rescueData.locationID ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                            operationType: (_rescueData_operationType2 = rescueData.operationType) === null || _rescueData_operationType2 === void 0 ? void 0 : _rescueData_operationType2.map((type)=>type.value).join(\",\"),\n                            operationDescription: rescueData.operationDescription,\n                            vesselTypeDescription: rescueData.vesselTypeDescription\n                        }\n                    }\n                });\n            }\n        }\n    };\n    // Expose the save function to parent component\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (onSaveFunctionReady) {\n            onSaveFunctionReady(handleSave);\n        }\n    }, [\n        onSaveFunctionReady,\n        handleSave\n    ]);\n    const [createEventType_VesselRescue] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateEventType_VesselRescue, {\n        onCompleted: (response)=>{\n            const data = response.createEventType_VesselRescue;\n            createCGEventMission({\n                variables: {\n                    input: {\n                        missionType: missionData.missionType,\n                        description: missionData.description,\n                        operationDescription: missionData.operationDescription,\n                        operationOutcome: missionData.operationOutcome,\n                        completedAt: time,\n                        currentLocationID: rescueData.locationID ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                        eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                        eventType: \"VesselRescue\"\n                    }\n                }\n            });\n            handleSaveParent(+(data === null || data === void 0 ? void 0 : data.id), 0);\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating vessel rescue\", error);\n        }\n    });\n    const [updateEventType_VesselRescue] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateEventType_VesselRescue, {\n        onCompleted: (response)=>{\n            const data = response.updateEventType_VesselRescue;\n            if (rescueData.missionID > 0) {\n                var _currentMissionLocation_latitude, _currentMissionLocation_longitude;\n                updateCGEventMission({\n                    variables: {\n                        input: {\n                            id: rescueData.missionID,\n                            missionType: missionData.missionType,\n                            description: missionData.description,\n                            operationDescription: missionData.operationDescription,\n                            operationOutcome: missionData.operationOutcome,\n                            completedAt: time,\n                            currentLocationID: rescueData.locationID,\n                            eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                            eventType: \"VesselRescue\",\n                            lat: (_currentMissionLocation_latitude = currentMissionLocation.latitude) === null || _currentMissionLocation_latitude === void 0 ? void 0 : _currentMissionLocation_latitude.toString(),\n                            long: (_currentMissionLocation_longitude = currentMissionLocation.longitude) === null || _currentMissionLocation_longitude === void 0 ? void 0 : _currentMissionLocation_longitude.toString()\n                        }\n                    }\n                });\n            } else {\n                createCGEventMission({\n                    variables: {\n                        input: {\n                            missionType: missionData.missionType,\n                            description: missionData.description,\n                            operationDescription: missionData.operationDescription,\n                            operationOutcome: missionData.operationOutcome,\n                            completedAt: time,\n                            currentLocationID: rescueData.currentLocationID,\n                            eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                            eventType: \"VesselRescue\",\n                            lat: currentMissionLocation.latitude.toString(),\n                            long: currentMissionLocation.longitude.toString()\n                        }\n                    }\n                });\n            }\n            handleSaveParent(+currentRescueID, 0);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating vessel rescue\", error);\n        }\n    });\n    const [createCGEventMission] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateCGEventMission, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"Error creating CG Event Mission\", error);\n        }\n    });\n    const [updateCGEventMission] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateCGEventMission, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"Error updating CG Event Mission\", error);\n        }\n    });\n    const handleLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setRescueData((prev)=>{\n                return {\n                    ...prev,\n                    locationID: +value.value,\n                    latitude: null,\n                    longitude: null\n                };\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setRescueData((prev)=>{\n                return {\n                    ...prev,\n                    locationID: 0,\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                };\n            });\n        }\n    };\n    const handleMissionLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setMissionData({\n                ...missionData,\n                currentLocationID: +value.value,\n                lat: null,\n                long: null\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setMissionData({\n                ...missionData,\n                currentLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n        }\n    };\n    const handleCreateComment = ()=>{\n        if (selectedEvent) {\n            setOpenCommentsDialog(true);\n            handleEditorChange(\"\");\n            setCommentData(false);\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_19__.toast.error(\"Please save the event first in order to create timeline!\");\n        }\n    };\n    const handleEditComment = (comment)=>{\n        setOpenCommentsDialog(true);\n        setCommentData(comment);\n        handleEditorChange(comment.description);\n        setCommentTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(comment.time).format(\"HH:mm\"));\n    };\n    const handleDeleteComment = (commentId)=>{\n        const comment = timeline === null || timeline === void 0 ? void 0 : timeline.find((c)=>c.id === commentId);\n        if (comment) {\n            setDeleteCommentsDialog(true);\n            setCommentData(comment);\n        }\n    };\n    const handleDeleteComments = async ()=>{\n        if (offline) {\n            await missionTimelineModel.save({\n                id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                archived: true\n            });\n            setOpenCommentsDialog(false);\n            setDeleteCommentsDialog(false);\n            getCurrentEvent(currentRescueID);\n            setDeleteCommentsDialog(false);\n        } else {\n            updateMissionTimeline({\n                variables: {\n                    input: {\n                        id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                        archived: true\n                    }\n                }\n            });\n            setDeleteCommentsDialog(false);\n        }\n    };\n    const offlineGetSeaLogsMembersList = async ()=>{\n        // getSeaLogsMembersList(handleSetMemberList)\n        const members = await memberModel.getAll();\n        handleSetMemberList(members);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (offline) {\n            offlineGetSeaLogsMembersList();\n        }\n    }, [\n        offline\n    ]);\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_6__.getVesselByID)(+vesselID, setVessel, offline);\n    const [getSectionCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            let data = response.readCrewMembers_LogBookEntrySections.nodes;\n            setCrewMembers(data);\n        },\n        onError: (error)=>{\n            console.error(\"CrewMembers_LogBookEntrySection error\", error);\n        }\n    });\n    const handleSetLogbook = async (logbook)=>{\n        setLogbook(logbook);\n        const sectionTypes = Array.from(new Set(logbook.logBookEntrySections.nodes.map((sec)=>sec.className))).map((type)=>({\n                className: type,\n                ids: logbook.logBookEntrySections.nodes.filter((sec)=>sec.className === type).map((sec)=>sec.id)\n            }));\n        sectionTypes.forEach(async (section)=>{\n            if (section.className === \"SeaLogs\\\\CrewMembers_LogBookEntrySection\") {\n                if (offline) {\n                    const data = await cmlbsModel.getByIds(section.ids);\n                    setCrewMembers(data);\n                } else {\n                    const searchFilter = {};\n                    searchFilter.id = {\n                        in: section.ids\n                    };\n                    getSectionCrewMembers_LogBookEntrySection({\n                        variables: {\n                            filter: searchFilter\n                        }\n                    });\n                }\n            }\n        });\n    };\n    const getLogBookEntryByID = async (id)=>{\n        if (offline) {\n            const data = await logbookModel.getById(id);\n            if (data) {\n                handleSetLogbook(data);\n            }\n        } else {\n            queryLogBookEntry({\n                variables: {\n                    logbookEntryId: +id\n                }\n            });\n        }\n    };\n    const [queryLogBookEntry] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GET_LOGBOOK_ENTRY_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneLogBookEntry;\n            if (data) {\n                handleSetLogbook(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryLogBookEntry error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        getLogBookEntryByID(+logentryID);\n    }, []);\n    const [queryVesselCrews] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CREW_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers;\n            if (data) {\n                const allMembers = data.nodes.filter((item)=>{\n                    return +item.id !== +masterID;\n                }).map((member)=>{\n                    // const crewWithTraining = GetCrewListWithTrainingStatus(\n                    //     [member],\n                    //     [vessel],\n                    // )[0]\n                    return {\n                        label: \"\".concat(member.firstName || \"\", \" \").concat(member.surname || \"\").trim(),\n                        value: member.id,\n                        // data: crewWithTraining,\n                        profile: {\n                            firstName: member.firstName,\n                            surname: member.surname,\n                            avatar: member.profileImage\n                        }\n                    };\n                });\n                setAllMembers(allMembers);\n                const members = allMembers.filter((member)=>{\n                    if (!crewMembers) {\n                        return true;\n                    }\n                    return !Array.isArray(crewMembers) || !crewMembers.some((section)=>section && section.crewMember && section.crewMember.id === member.value && section.punchOut === null);\n                });\n                const memberOptions = members.filter((member)=>!crewMembersList || !Array.isArray(crewMembersList) || !crewMembersList.includes(+member.value));\n                setCrewMemberOptions(memberOptions);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVesselCrews error\", error);\n        }\n    });\n    const loadVesselCrews = async ()=>{\n        if (offline) {\n            const data = await seaLogsMemberModel.getByVesselId(vesselID);\n            setAllVesselCrews(data);\n            if (data) {\n                const members = data.filter((item)=>+item.id !== +logbook.master.id).map((member)=>{\n                    const crewWithTraining = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_6__.GetCrewListWithTrainingStatus)([\n                        member\n                    ], [\n                        vessel\n                    ])[0];\n                    return {\n                        label: \"\".concat(member.firstName || \"\", \" \").concat(member.surname || \"\").trim(),\n                        value: member.id,\n                        data: crewWithTraining,\n                        profile: {\n                            firstName: member.firstName,\n                            surname: member.surname,\n                            avatar: member.profileImage\n                        }\n                    };\n                });\n                setCrewMemberOptions(members);\n            }\n        } else {\n            await queryVesselCrews({\n                variables: {\n                    filter: {\n                        vehicles: {\n                            id: {\n                                eq: vesselID\n                            }\n                        },\n                        isArchived: {\n                            eq: false\n                        }\n                    }\n                }\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isLoading) {\n            loadVesselCrews();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" pt-0\"),\n        children: [\n            type === \"TaskingComplete\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-3 gap-6 pb-0 pt-0 px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-0  col-span-3 md:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_21__.H3, {\n                                    children: \"Mission complete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1128,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_21__.P, {\n                                    children: \"Record the operation outcome, location and time of completion\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1129,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1127,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-3 md:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__.Combobox, {\n                                        options: operationOutcomes,\n                                        value: operationOutcomes === null || operationOutcomes === void 0 ? void 0 : operationOutcomes.find((outcome)=>outcome.value == (missionData === null || missionData === void 0 ? void 0 : missionData.operationOutcome)),\n                                        onChange: (value)=>{\n                                            setMissionData({\n                                                ...missionData,\n                                                operationOutcome: value === null || value === void 0 ? void 0 : value.value\n                                            });\n                                        },\n                                        placeholder: \"Operation outcome\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1136,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1135,\n                                    columnNumber: 29\n                                }, this),\n                                (missionData === null || missionData === void 0 ? void 0 : missionData.operationOutcome) == \"Other\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                                        id: \"operation-outcome-description\",\n                                        rows: 4,\n                                        className: \"\",\n                                        placeholder: \"Description\",\n                                        value: (missionData === null || missionData === void 0 ? void 0 : missionData.operationDescription) ? missionData === null || missionData === void 0 ? void 0 : missionData.operationDescription : \"\",\n                                        onChange: ()=>{\n                                            setMissionData({\n                                                ...missionData,\n                                                operationDescription: document.getElementById(\"operation-outcome-description\").value\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1154,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1153,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1134,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                    lineNumber: 1126,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 pb-4 pt-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2.5\",\n                        children: [\n                            timeline && (timeline === null || timeline === void 0 ? void 0 : timeline.map((comment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_task_task__WEBPACK_IMPORTED_MODULE_27__.RecordCard, {\n                                    record: comment,\n                                    onEdit: handleEditComment,\n                                    onDelete: handleDeleteComment\n                                }, \"\".concat(comment.id, \"-record-\").concat(comment.time || \"\"), false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1186,\n                                    columnNumber: 33\n                                }, this))),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                                        onClick: ()=>setShowInputDetailsPanel(true),\n                                        children: \"Vessel details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1194,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Tooltip, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.TooltipTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                                                    onClick: handleCreateComment,\n                                                    disabled: !selectedEvent,\n                                                    children: \"Add notes/comments\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                    lineNumber: 1201,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1200,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.TooltipContent, {\n                                                hidden: (selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id) > 0,\n                                                children: \"Please save the event first in order to create timeline!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1207,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1199,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                lineNumber: 1193,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                        lineNumber: 1183,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                    lineNumber: 1182,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1181,\n                columnNumber: 13\n            }, this),\n            showSaveButtons && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n                        onClick: ()=>closeModal(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                        lineNumber: 1218,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                        onClick: handleSave,\n                        children: \"Save\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                        lineNumber: 1224,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1217,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_18__.AlertDialogNew, {\n                openDialog: openCommentsDialog,\n                setOpenDialog: setOpenCommentsDialog,\n                size: \"xl\",\n                handleCreate: handleSaveComments,\n                title: (commentData === null || commentData === void 0 ? void 0 : commentData.id) > 0 ? \"Update Comment\" : \"Create Comment\",\n                actionText: (commentData === null || commentData === void 0 ? void 0 : commentData.id) > 0 ? \"Update\" : \"Create Comment\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                            htmlFor: \"comment-type\",\n                            label: \"Comment Type\",\n                            className: \"text-sm font-medium\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__.Combobox, {\n                                id: \"comment-type\",\n                                options: commentTypes,\n                                value: commentTypes === null || commentTypes === void 0 ? void 0 : commentTypes.find((type)=>{\n                                    var _commentData_commentType;\n                                    return type.value == (commentData === null || commentData === void 0 ? void 0 : (_commentData_commentType = commentData.commentType) === null || _commentData_commentType === void 0 ? void 0 : _commentData_commentType.replaceAll(\"_\", \" \"));\n                                }),\n                                onChange: (value)=>setCommentData({\n                                        ...commentData,\n                                        commentType: value === null || value === void 0 ? void 0 : value.value\n                                    }),\n                                placeholder: \"Select comment type\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                lineNumber: 1241,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1237,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                    htmlFor: \"comment_time\",\n                                    className: \"flex items-center gap-2\",\n                                    children: \"Time of Completion\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1263,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    time: commentTime,\n                                    handleTimeChange: (date)=>{\n                                        handleCommentTimeChange(date);\n                                    },\n                                    timeID: \"comment_time\",\n                                    fieldName: \"comment_time\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1268,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1262,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                    htmlFor: \"comment\",\n                                    children: \"Comment Content\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1279,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    id: \"comment\",\n                                    placeholder: \"Write your comment here...\",\n                                    className: \"w-full min-h-[150px] bg-secondary-foreground\",\n                                    content: content,\n                                    handleEditorChange: handleEditorChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1280,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1278,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                    htmlFor: \"author\",\n                                    className: \"flex items-center gap-2\",\n                                    children: \"Author\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1290,\n                                    columnNumber: 25\n                                }, this),\n                                members && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__.Combobox, {\n                                    id: \"author\",\n                                    options: crewMemberOptions,\n                                    value: crewMemberOptions === null || crewMemberOptions === void 0 ? void 0 : crewMemberOptions.find((member)=>{\n                                        var _commentData_author;\n                                        return member.value == (commentData === null || commentData === void 0 ? void 0 : (_commentData_author = commentData.author) === null || _commentData_author === void 0 ? void 0 : _commentData_author.id);\n                                    }),\n                                    onChange: (value)=>setCommentData({\n                                            ...commentData,\n                                            authorID: value === null || value === void 0 ? void 0 : value.value\n                                        }),\n                                    placeholder: \"Select crew\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1296,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1289,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                    lineNumber: 1236,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1227,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_18__.AlertDialogNew, {\n                openDialog: deleteCommentsDialog,\n                setOpenDialog: setDeleteCommentsDialog,\n                handleCreate: handleDeleteComments,\n                title: \"Delete Comment\",\n                variant: \"warning\",\n                actionText: \"Confirm delete\",\n                children: \"Are you sure you want to delete this comment? This action cannot be undone and all associated data will be permanently removed from the system.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1315,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_20__.Sheet, {\n                open: showInputDetailsP,\n                onOpenChange: (open)=>setShowInputDetailsPanel(open),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_20__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-full max-w-md sm:max-w-xl bg-background phablet:bg-muted\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_20__.SheetHeader, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1332,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col h-full min-h-[400px] overflow-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-grow space-y-6 py-4 mx-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.CardHeader, {\n                                                        className: \"pb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"my-4 text-sm font-semibold uppercase\",\n                                                                    children: \"Target Vessel Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                    lineNumber: 1340,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1339,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_21__.P, {\n                                                                children: \"Record vessel name, callsign and number of people on board\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1344,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1338,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.CardContent, {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"vessel-name\",\n                                                                        children: \"Vessel Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1351,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                        id: \"vessel-name\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Enter vessel name\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselName) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                vesselName: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1354,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1350,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"call-sign\",\n                                                                        children: \"Call Sign\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1372,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                        id: \"call-sign\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Enter call sign\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.callSign) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                callSign: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1375,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1371,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"pob\",\n                                                                        children: \"People On Board (POB)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1393,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                        id: \"pob\",\n                                                                        type: \"number\",\n                                                                        placeholder: \"Enter number of people\",\n                                                                        min: 1,\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.pob) || 0,\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                pob: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1396,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1392,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1349,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1337,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.CardHeader, {\n                                                        className: \"pb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"my-4 text-sm font-semibold uppercase\",\n                                                                    children: \"Vessel Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                    lineNumber: 1417,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1416,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_21__.P, {\n                                                                children: \"Include details of vessel type, make and descriptors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1421,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1415,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.CardContent, {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"vessel-length\",\n                                                                        children: \"Number of Vessels\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1428,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                        id: \"vessel-length\",\n                                                                        type: \"number\",\n                                                                        placeholder: \"Enter vessel length\",\n                                                                        min: 1,\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselLength) || 0,\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                vesselLength: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1431,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1427,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"vessel-type\",\n                                                                        children: \"Vessel type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1451,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__.Combobox, {\n                                                                        options: vesselTypes,\n                                                                        value: vesselTypes === null || vesselTypes === void 0 ? void 0 : vesselTypes.find((type)=>type.value == (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselType)),\n                                                                        onChange: (value)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                vesselType: value === null || value === void 0 ? void 0 : value.value\n                                                                            });\n                                                                        },\n                                                                        placeholder: \"Select vessel type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1454,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1450,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselType) == \"Other\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"vessel-type-description\",\n                                                                        children: \"Vessel type description\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1474,\n                                                                        columnNumber: 49\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                                                                        id: \"vessel-type-description\",\n                                                                        rows: 3,\n                                                                        placeholder: \"Describe the vessel type\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselTypeDescription) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                vesselTypeDescription: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1477,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1473,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"make\",\n                                                                        children: \"Make and odel\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1497,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                        id: \"make\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Enter make and model\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.makeAndModel) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                makeAndModel: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1500,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1496,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"color\",\n                                                                        children: \"Color\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1519,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                        id: \"color\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Enter vessel color\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.color) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                color: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1520,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1518,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1426,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1414,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.CardHeader, {\n                                                        className: \"pb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"my-4 text-sm font-semibold uppercase\",\n                                                                    children: \"Owner's Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                    lineNumber: 1540,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1539,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_21__.P, {\n                                                                children: \"Record vessel owner's details and membership number if applicable\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1544,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1538,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.CardContent, {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                                htmlFor: \"owner-name\",\n                                                                                children: \"Owner's Name\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1552,\n                                                                                columnNumber: 49\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                                id: \"owner-name\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter owner's name\",\n                                                                                value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.ownerName) || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    setRescueData({\n                                                                                        ...rescueData,\n                                                                                        ownerName: e.target.value\n                                                                                    });\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1555,\n                                                                                columnNumber: 49\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1551,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                                htmlFor: \"owner-phone\",\n                                                                                children: \"Phone Number\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1574,\n                                                                                columnNumber: 49\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                                id: \"owner-phone\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter phone number\",\n                                                                                value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.phone) || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    setRescueData({\n                                                                                        ...rescueData,\n                                                                                        phone: e.target.value\n                                                                                    });\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1577,\n                                                                                columnNumber: 49\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1573,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1550,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                                htmlFor: \"cgnz\",\n                                                                                children: \"Coastguard NZ Membership\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1597,\n                                                                                columnNumber: 49\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                                id: \"cgnz\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter membership number\",\n                                                                                value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.cgMembership) || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    setRescueData({\n                                                                                        ...rescueData,\n                                                                                        cgMembership: e.target.value\n                                                                                    });\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1600,\n                                                                                columnNumber: 49\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1596,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                                htmlFor: \"owner-email\",\n                                                                                children: \"Email Address\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1619,\n                                                                                columnNumber: 49\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                                id: \"owner-email\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter email address\",\n                                                                                value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.email) || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    setRescueData({\n                                                                                        ...rescueData,\n                                                                                        email: e.target.value\n                                                                                    });\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1622,\n                                                                                columnNumber: 49\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1618,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1595,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"owner-address\",\n                                                                        children: \"Owner's Address\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1641,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                                                                        id: \"owner-address\",\n                                                                        rows: 3,\n                                                                        placeholder: \"Enter owner's address\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.address) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                address: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1644,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1640,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 pt-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                    htmlFor: \"owner-onboard\",\n                                                                    className: \"cursor-pointer\",\n                                                                    label: \"Is the owner on-board?\",\n                                                                    leftContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_23__.Checkbox, {\n                                                                        id: \"owner-onboard\",\n                                                                        checked: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.ownerOnBoard) || false,\n                                                                        size: \"lg\",\n                                                                        isRadioStyle: true,\n                                                                        onCheckedChange: (checked)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                ownerOnBoard: checked === true\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1666,\n                                                                        columnNumber: 53\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                    lineNumber: 1661,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1660,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1549,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1537,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1335,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1334,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                                        onClick: ()=>setShowInputDetailsPanel(false),\n                                        children: \"Save Changes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1695,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1693,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1333,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                    lineNumber: 1329,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1326,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n        lineNumber: 1123,\n        columnNumber: 9\n    }, this);\n}\n_s(VesselRescueFields, \"k0T/zvRMstdbKOyCfeAohU57b1U=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_28__.useMediaQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery\n    ];\n});\n_c = VesselRescueFields;\nvar _c;\n$RefreshReg$(_c, \"VesselRescueFields\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue-fields.tsx\n"));

/***/ })

});