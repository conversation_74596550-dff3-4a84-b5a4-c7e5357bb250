'use client'
import { useLazyQuery, useMutation } from '@apollo/client'
import { GET_RADIO_LOGS } from '@/app/lib/graphQL/query'
import { CREATE_RADIO_LOG, UPDATE_RADIO_LOG } from '@/app/lib/graphQL/mutation'
import React, { useEffect, useState } from 'react'
import { X, Plus, Trash2, SquareArrowOutUpRight } from 'lucide-react'

import { SheetAlertDialog } from '@/components/ui/sheet-alert-dialog'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import {
    Sheet,
    SheetBody,
    SheetContent,
    SheetHeader,
    SheetTitle,
} from '@/components/ui/sheet'
import { useSearchParams } from 'next/navigation'
import RadioTimeField from './components/radioTimeField'
import dayjs from 'dayjs'
import { <PERSON><PERSON>, Separator } from '@/components/ui'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'
export default function RadioLogs({
    open,
    setOpen,
    logentryID,
}: {
    open: any
    setOpen: any
    logentryID: any
}) {
    const searchParams = useSearchParams()
    const vesselID = searchParams.get('vesselID') ?? 0
    const [radioLogs, setRadioLogs] = useState<any>([])
    const [defaultRadioLogs, setDefaultRadioLogs] = useState<any>(false)
    const [displayLogAlert, setDisplayLogAlert] = useState(false)
    const [radioTitle, setRadioTitle] = useState('')
    const [currentComment, setCurrentComment] = useState<any>('')
    const [currentLog, setCurrentLog] = useState<any>(false)
    const [openCommentAlert, setOpenCommentAlert] = useState(false)

    const bp = useBreakpoints()

    const [getRadioLogs] = useLazyQuery(GET_RADIO_LOGS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readRadioLogs.nodes
            if (data) {
                setRadioLogs(data)
                if (defaultRadioLogs == false) {
                    getDefaultRadioLogs({
                        variables: {
                            filter: {
                                vesselID: { eq: +vesselID },
                            },
                        },
                    })
                }
            }
        },
        onError: (error: any) => {
            console.error('readRadioLogs error', error)
        },
    })

    const [getDefaultRadioLogs] = useLazyQuery(GET_RADIO_LOGS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readRadioLogs.nodes
            if (data) {
                setDefaultRadioLogs(true)
                if (data.length > 0) {
                    const logsToCreate = data
                        .filter(
                            (defaultLog: any) =>
                                !radioLogs.some(
                                    (log: any) =>
                                        log.defaultParent == defaultLog.id,
                                ),
                        )
                        .map((defaultLog: any) => ({
                            title: defaultLog.title,
                            logBookEntryID: logentryID,
                            defaultParent: +defaultLog.id,
                        }))

                    logsToCreate.forEach((log: any) => {
                        createRadioLog({
                            variables: {
                                input: {
                                    logBookEntryID: +logentryID,
                                    title: log.title,
                                    defaultParent: +log.defaultParent,
                                },
                            },
                        })
                    })
                }
            }
        },
        onError: (error: any) => {
            console.error('readRadioLogs error', error)
        },
    })

    useEffect(() => {
        if (logentryID) {
            getRadioLogs({
                variables: {
                    filter: {
                        logBookEntryID: { eq: logentryID },
                    },
                },
            })
        }
    }, [])

    useEffect(() => {
        if (defaultRadioLogs && defaultRadioLogs.length > 0) {
        }
    }, [defaultRadioLogs])

    const [createRadioLog] = useMutation(CREATE_RADIO_LOG, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.createRadioLog
            if (data) {
                getRadioLogs({
                    variables: {
                        filter: {
                            logBookEntryID: { eq: logentryID },
                        },
                    },
                })
                setDisplayLogAlert(false)
            }
        },
        onError: (error: any) => {
            console.error('createRadioLog error', error)
        },
    })

    const [updateRadioLog] = useMutation(UPDATE_RADIO_LOG, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.updateRadioLog
            if (data) {
                getRadioLogs({
                    variables: {
                        filter: {
                            logBookEntryID: { eq: logentryID },
                        },
                    },
                })
                setDisplayLogAlert(false)
            }
        },
        onError: (error: any) => {
            console.error('updateRadioLog error', error)
        },
    })

    const handleAddRadioLog = () => {
        setDisplayLogAlert(false)
        if (currentLog) {
            updateRadioLog({
                variables: {
                    input: {
                        id: currentLog.id,
                        title: radioTitle,
                    },
                },
            })
        } else {
            createRadioLog({
                variables: {
                    input: {
                        logBookEntryID: +logentryID,
                        title: radioTitle,
                    },
                },
            })
        }
    }

    const handleLogCheck = (log: any, time: any) => {
        updateRadioLog({
            variables: {
                input: {
                    id: log.id,
                    time: dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
                },
            },
        })
    }

    return (
        <div className="w-full pb-16">
            <Sheet open={open} onOpenChange={setOpen}>
                <SheetContent className="" side="right">
                    <SheetHeader>
                        <SheetTitle>Radio logs</SheetTitle>
                    </SheetHeader>
                    <SheetBody className="flex flex-col px-6 py-4 h-full items-start">
                        {radioLogs && radioLogs.length > 0 ? (
                            <div className="w-full">
                                {radioLogs.map((log: any, i: number) => (
                                    <React.Fragment key={log.id}>
                                        {i > 0 && (
                                            <Separator className="my-4" />
                                        )}
                                        <div className="flex flex-col md:flex-row gap-2 mb-2 justify-between md:items-center">
                                            <span className="text-sm lg:text-base">
                                                <Button
                                                    variant="link"
                                                    iconRight={
                                                        SquareArrowOutUpRight
                                                    }
                                                    className="p-0 font-normal text-base text-input"
                                                    onClick={() => {
                                                        setDisplayLogAlert(true)
                                                        setCurrentLog(log)
                                                    }}>
                                                    {log.title}
                                                </Button>
                                            </span>
                                            <div className="flex flex-row items-center gap-2.5">
                                                <RadioTimeField
                                                    log={log}
                                                    handleTimeChange={
                                                        handleLogCheck
                                                    }
                                                />
                                                <Button
                                                    variant="destructive"
                                                    iconLeft={
                                                        bp.small ? Trash2 : null
                                                    }
                                                    onClick={() => {
                                                        updateRadioLog({
                                                            variables: {
                                                                input: {
                                                                    id: log.id,
                                                                    logBookEntryID: 0,
                                                                },
                                                            },
                                                        })
                                                    }}>
                                                    {bp.small ? null : 'Delete'}
                                                </Button>
                                            </div>
                                        </div>
                                    </React.Fragment>
                                ))}
                            </div>
                        ) : (
                            <div className="flex justify-center items-start h-full">
                                <p className="text-gray-500">No Radio Logs</p>
                            </div>
                        )}
                        <Button
                            iconLeft={Plus}
                            onClick={() => {
                                setDisplayLogAlert(true)
                                setCurrentLog(false)
                            }}>
                            Add Radio Log
                        </Button>
                    </SheetBody>
                </SheetContent>
            </Sheet>

            <SheetAlertDialog
                openDialog={displayLogAlert}
                setOpenDialog={setDisplayLogAlert}
                handleCreate={handleAddRadioLog}
                actionText={currentLog ? 'Update' : 'Create'}
                title={`${currentLog ? 'Edit' : 'Create'} Radio Log`}
                sheetContext={true}>
                <div className="my-4">
                    <div className="flex flex-col w-full space-y-2">
                        <Label label="Location/Title">
                            <Input
                                type="text"
                                id="radioLogTitle"
                                placeholder="Enter Location/Title"
                                defaultValue={currentLog?.title}
                                required
                                onChange={(e) => {
                                    setRadioTitle(e.target.value)
                                }}
                            />
                        </Label>
                    </div>
                </div>
            </SheetAlertDialog>

            <SheetAlertDialog
                openDialog={openCommentAlert}
                setOpenDialog={setOpenCommentAlert}
                handleCreate={() => {
                    updateRadioLog({
                        variables: {
                            input: {
                                id: currentLog.id,
                                comment: currentComment,
                            },
                        },
                    })
                    setOpenCommentAlert(false)
                }}
                title="Comment"
                actionText="Update"
                sheetContext={true}>
                <div className="my-4">
                    <Label label="Comment">
                        <Textarea
                            id="radioLogComment"
                            placeholder="Enter Comment"
                            defaultValue={currentComment}
                            rows={4}
                            required
                            onChange={(e) => {
                                setCurrentComment(e.target.value)
                            }}
                        />
                    </Label>
                </div>
            </SheetAlertDialog>
        </div>
    )
}
