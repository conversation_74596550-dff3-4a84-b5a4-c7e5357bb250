"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue-fields.tsx":
/*!***********************************************************!*\
  !*** ./src/app/ui/logbook/forms/vessel-rescue-fields.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _app_offline_models_eventType_VesselRescue__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/eventType_VesselRescue */ \"(app-pages-browser)/./src/app/offline/models/eventType_VesselRescue.js\");\n/* harmony import */ var _app_offline_models_cgEventMission__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/cgEventMission */ \"(app-pages-browser)/./src/app/offline/models/cgEventMission.js\");\n/* harmony import */ var _app_offline_models_missionTimeline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/missionTimeline */ \"(app-pages-browser)/./src/app/offline/models/missionTimeline.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/app/offline/models/logBookEntry */ \"(app-pages-browser)/./src/app/offline/models/logBookEntry.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _app_ui_maintenance_task_task__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/app/ui/maintenance/task/task */ \"(app-pages-browser)/./src/app/ui/maintenance/task/task.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst VesselRescueFields = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(_c = _s((param, ref)=>{\n    let { geoLocations, selectedEvent = false, closeModal, handleSaveParent, currentRescueID, type, eventCurrentLocation, locationDescription, setLocationDescription, offline = false, locked = false, showSaveButtons = true } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"HH:mm\"));\n    const [openCommentsDialog, setOpenCommentsDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [commentTime, setCommentTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [rescueData, setRescueData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [missionData, setMissionData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [commentData, setCommentData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [timeline, setTimeline] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [deleteCommentsDialog, setDeleteCommentsDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [allVesselCrews, setAllVesselCrews] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [vessel, setVessel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [masterID, setMasterID] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [crewMemberOptions, setCrewMemberOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [allMembers, setAllMembers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [crewMembers, setCrewMembers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [crewMembersList, setCrewMembersList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [currentMissionLocation, setCurrentMissionLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [showInputDetailsP, setShowInputDetailsPanel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_28__.useMediaQuery)(\"(min-width: 640px)\");\n    const memberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const vesselRescueModel = new _app_offline_models_eventType_VesselRescue__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\n    const cgEventMissionModel = new _app_offline_models_cgEventMission__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const missionTimelineModel = new _app_offline_models_missionTimeline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const cmlbsModel = new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_24__[\"default\"]();\n    const logbookModel = new _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_25__[\"default\"]();\n    const handleTimeChange = (date)=>{\n        setTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    };\n    var _searchParams_get;\n    const vesselID = (_searchParams_get = searchParams.get(\"vesselID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    var _searchParams_get1;\n    const logentryID = (_searchParams_get1 = searchParams.get(\"logentryID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setRescueData(false);\n        if (currentRescueID) {\n            getCurrentEvent(currentRescueID);\n        }\n    }, [\n        currentRescueID\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setCurrentLocation(eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.currentLocation);\n        handleLocationChange({\n            value: eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.geoLocationID\n        });\n    }, [\n        eventCurrentLocation\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (rescueData) {\n            setRescueData((prev)=>{\n                return {\n                    ...prev,\n                    locationDescription\n                };\n            });\n        }\n    }, [\n        locationDescription\n    ]);\n    const getCurrentEvent = async (currentRescueID)=>{\n        if (currentRescueID > 0) {\n            if (offline) {\n                const event = await vesselRescueModel.getById(currentRescueID);\n                if (event) {\n                    var _event_mission, _event_mission1, _event_mission2, _event_mission_missionType, _event_mission3, _event_mission4, _event_mission_operationOutcome, _event_mission5, _event_mission_currentLocation, _event_mission6, _event_mission7, _event_mission_currentLocation1, _event_mission8, _event_mission_currentLocation2, _event_mission9, _event_missionTimeline, _event_mission_currentLocation3, _event_mission10, _event_mission_currentLocation4, _event_mission11;\n                    setRescueData({\n                        vesselName: (event === null || event === void 0 ? void 0 : event.vesselName) ? event === null || event === void 0 ? void 0 : event.vesselName : \"\",\n                        callSign: (event === null || event === void 0 ? void 0 : event.callSign) ? event === null || event === void 0 ? void 0 : event.callSign : \"\",\n                        pob: (event === null || event === void 0 ? void 0 : event.pob) ? event === null || event === void 0 ? void 0 : event.pob : \"\",\n                        latitude: (event === null || event === void 0 ? void 0 : event.latitude) ? event === null || event === void 0 ? void 0 : event.latitude : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.latitude,\n                        longitude: (event === null || event === void 0 ? void 0 : event.longitude) ? event === null || event === void 0 ? void 0 : event.longitude : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.longitude,\n                        locationDescription: (event === null || event === void 0 ? void 0 : event.locationDescription) ? event === null || event === void 0 ? void 0 : event.locationDescription : \"\",\n                        vesselLength: (event === null || event === void 0 ? void 0 : event.vesselLength) ? event === null || event === void 0 ? void 0 : event.vesselLength : \"\",\n                        vesselType: (event === null || event === void 0 ? void 0 : event.vesselType) ? event === null || event === void 0 ? void 0 : event.vesselType : \"\",\n                        makeAndModel: (event === null || event === void 0 ? void 0 : event.makeAndModel) ? event === null || event === void 0 ? void 0 : event.makeAndModel : \"\",\n                        color: (event === null || event === void 0 ? void 0 : event.color) ? event === null || event === void 0 ? void 0 : event.color : \"\",\n                        ownerName: (event === null || event === void 0 ? void 0 : event.ownerName) ? event === null || event === void 0 ? void 0 : event.ownerName : \"\",\n                        phone: (event === null || event === void 0 ? void 0 : event.phone) ? event === null || event === void 0 ? void 0 : event.phone : \"\",\n                        email: (event === null || event === void 0 ? void 0 : event.email) ? event === null || event === void 0 ? void 0 : event.email : \"\",\n                        address: (event === null || event === void 0 ? void 0 : event.address) ? event === null || event === void 0 ? void 0 : event.address : \"\",\n                        ownerOnBoard: (event === null || event === void 0 ? void 0 : event.ownerOnBoard) ? event === null || event === void 0 ? void 0 : event.ownerOnBoard : false,\n                        cgMembership: (event === null || event === void 0 ? void 0 : event.cgMembership) ? event === null || event === void 0 ? void 0 : event.cgMembership : \"\",\n                        locationID: (event === null || event === void 0 ? void 0 : event.vesselLocationID) ? event === null || event === void 0 ? void 0 : event.vesselLocationID : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.geoLocationID,\n                        missionID: (event === null || event === void 0 ? void 0 : (_event_mission = event.mission) === null || _event_mission === void 0 ? void 0 : _event_mission.id) ? event === null || event === void 0 ? void 0 : (_event_mission1 = event.mission) === null || _event_mission1 === void 0 ? void 0 : _event_mission1.id : \"\",\n                        operationType: (event === null || event === void 0 ? void 0 : event.operationType) ? operationType.filter((operation)=>event === null || event === void 0 ? void 0 : event.operationType.split(\",\").includes(operation.value)) : [],\n                        operationDescription: (event === null || event === void 0 ? void 0 : event.operationDescription) ? event === null || event === void 0 ? void 0 : event.operationDescription : \"\",\n                        vesselTypeDescription: (event === null || event === void 0 ? void 0 : event.vesselTypeDescription) ? event === null || event === void 0 ? void 0 : event.vesselTypeDescription : \"\"\n                    });\n                    setTime(event === null || event === void 0 ? void 0 : (_event_mission2 = event.mission) === null || _event_mission2 === void 0 ? void 0 : _event_mission2.completedAt);\n                    setMissionData({\n                        missionType: event === null || event === void 0 ? void 0 : (_event_mission3 = event.mission) === null || _event_mission3 === void 0 ? void 0 : (_event_mission_missionType = _event_mission3.missionType) === null || _event_mission_missionType === void 0 ? void 0 : _event_mission_missionType.replaceAll(\"_\", \" \"),\n                        description: event === null || event === void 0 ? void 0 : (_event_mission4 = event.mission) === null || _event_mission4 === void 0 ? void 0 : _event_mission4.description,\n                        operationOutcome: event === null || event === void 0 ? void 0 : (_event_mission5 = event.mission) === null || _event_mission5 === void 0 ? void 0 : (_event_mission_operationOutcome = _event_mission5.operationOutcome) === null || _event_mission_operationOutcome === void 0 ? void 0 : _event_mission_operationOutcome.replaceAll(\"_\", \" \"),\n                        currentLocationID: event === null || event === void 0 ? void 0 : (_event_mission6 = event.mission) === null || _event_mission6 === void 0 ? void 0 : (_event_mission_currentLocation = _event_mission6.currentLocation) === null || _event_mission_currentLocation === void 0 ? void 0 : _event_mission_currentLocation.id,\n                        operationDescription: event === null || event === void 0 ? void 0 : (_event_mission7 = event.mission) === null || _event_mission7 === void 0 ? void 0 : _event_mission7.operationDescription,\n                        lat: event === null || event === void 0 ? void 0 : (_event_mission8 = event.mission) === null || _event_mission8 === void 0 ? void 0 : (_event_mission_currentLocation1 = _event_mission8.currentLocation) === null || _event_mission_currentLocation1 === void 0 ? void 0 : _event_mission_currentLocation1.lat,\n                        long: event === null || event === void 0 ? void 0 : (_event_mission9 = event.mission) === null || _event_mission9 === void 0 ? void 0 : (_event_mission_currentLocation2 = _event_mission9.currentLocation) === null || _event_mission_currentLocation2 === void 0 ? void 0 : _event_mission_currentLocation2.long\n                    });\n                    setTimeline(event === null || event === void 0 ? void 0 : (_event_missionTimeline = event.missionTimeline) === null || _event_missionTimeline === void 0 ? void 0 : _event_missionTimeline.nodes);\n                    setCurrentLocation({\n                        latitude: event === null || event === void 0 ? void 0 : (_event_mission10 = event.mission) === null || _event_mission10 === void 0 ? void 0 : (_event_mission_currentLocation3 = _event_mission10.currentLocation) === null || _event_mission_currentLocation3 === void 0 ? void 0 : _event_mission_currentLocation3.lat,\n                        longitude: event === null || event === void 0 ? void 0 : (_event_mission11 = event.mission) === null || _event_mission11 === void 0 ? void 0 : (_event_mission_currentLocation4 = _event_mission11.currentLocation) === null || _event_mission_currentLocation4 === void 0 ? void 0 : _event_mission_currentLocation4.long\n                    });\n                    setCurrentMissionLocation({\n                        latitude: event === null || event === void 0 ? void 0 : event.lat,\n                        longitude: event === null || event === void 0 ? void 0 : event.long\n                    });\n                    setLocationDescription(event === null || event === void 0 ? void 0 : event.locationDescription);\n                }\n            } else {\n                getTripEvent({\n                    variables: {\n                        id: +currentRescueID\n                    }\n                });\n            }\n        }\n    };\n    const [getTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetTripEvent_VesselRescue, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneEventType_VesselRescue;\n            if (event) {\n                var _event_mission, _event_mission1, _event_mission2, _event_mission_missionType, _event_mission3, _event_mission4, _event_mission_operationOutcome, _event_mission5, _event_mission_currentLocation, _event_mission6, _event_mission7, _event_mission_currentLocation1, _event_mission8, _event_mission_currentLocation2, _event_mission9, _event_missionTimeline, _event_mission_currentLocation3, _event_mission10, _event_mission_currentLocation4, _event_mission11;\n                setRescueData({\n                    vesselName: (event === null || event === void 0 ? void 0 : event.vesselName) ? event === null || event === void 0 ? void 0 : event.vesselName : \"\",\n                    callSign: (event === null || event === void 0 ? void 0 : event.callSign) ? event === null || event === void 0 ? void 0 : event.callSign : \"\",\n                    pob: (event === null || event === void 0 ? void 0 : event.pob) ? event === null || event === void 0 ? void 0 : event.pob : \"\",\n                    latitude: (event === null || event === void 0 ? void 0 : event.latitude) ? event === null || event === void 0 ? void 0 : event.latitude : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.latitude,\n                    longitude: (event === null || event === void 0 ? void 0 : event.longitude) ? event === null || event === void 0 ? void 0 : event.longitude : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.longitude,\n                    locationDescription: (event === null || event === void 0 ? void 0 : event.locationDescription) ? event === null || event === void 0 ? void 0 : event.locationDescription : \"\",\n                    vesselLength: (event === null || event === void 0 ? void 0 : event.vesselLength) ? event === null || event === void 0 ? void 0 : event.vesselLength : \"\",\n                    vesselType: (event === null || event === void 0 ? void 0 : event.vesselType) ? event === null || event === void 0 ? void 0 : event.vesselType : \"\",\n                    makeAndModel: (event === null || event === void 0 ? void 0 : event.makeAndModel) ? event === null || event === void 0 ? void 0 : event.makeAndModel : \"\",\n                    color: (event === null || event === void 0 ? void 0 : event.color) ? event === null || event === void 0 ? void 0 : event.color : \"\",\n                    ownerName: (event === null || event === void 0 ? void 0 : event.ownerName) ? event === null || event === void 0 ? void 0 : event.ownerName : \"\",\n                    phone: (event === null || event === void 0 ? void 0 : event.phone) ? event === null || event === void 0 ? void 0 : event.phone : \"\",\n                    email: (event === null || event === void 0 ? void 0 : event.email) ? event === null || event === void 0 ? void 0 : event.email : \"\",\n                    address: (event === null || event === void 0 ? void 0 : event.address) ? event === null || event === void 0 ? void 0 : event.address : \"\",\n                    ownerOnBoard: (event === null || event === void 0 ? void 0 : event.ownerOnBoard) ? event === null || event === void 0 ? void 0 : event.ownerOnBoard : false,\n                    cgMembership: (event === null || event === void 0 ? void 0 : event.cgMembership) ? event === null || event === void 0 ? void 0 : event.cgMembership : \"\",\n                    locationID: (event === null || event === void 0 ? void 0 : event.vesselLocationID) ? event === null || event === void 0 ? void 0 : event.vesselLocationID : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.geoLocationID,\n                    missionID: (event === null || event === void 0 ? void 0 : (_event_mission = event.mission) === null || _event_mission === void 0 ? void 0 : _event_mission.id) ? event === null || event === void 0 ? void 0 : (_event_mission1 = event.mission) === null || _event_mission1 === void 0 ? void 0 : _event_mission1.id : \"\",\n                    operationType: (event === null || event === void 0 ? void 0 : event.operationType) ? operationType.filter((operation)=>event === null || event === void 0 ? void 0 : event.operationType.split(\",\").includes(operation.value)) : [],\n                    operationDescription: (event === null || event === void 0 ? void 0 : event.operationDescription) ? event === null || event === void 0 ? void 0 : event.operationDescription : \"\",\n                    vesselTypeDescription: (event === null || event === void 0 ? void 0 : event.vesselTypeDescription) ? event === null || event === void 0 ? void 0 : event.vesselTypeDescription : \"\"\n                });\n                setTime(event === null || event === void 0 ? void 0 : (_event_mission2 = event.mission) === null || _event_mission2 === void 0 ? void 0 : _event_mission2.completedAt);\n                setMissionData({\n                    missionType: event === null || event === void 0 ? void 0 : (_event_mission3 = event.mission) === null || _event_mission3 === void 0 ? void 0 : (_event_mission_missionType = _event_mission3.missionType) === null || _event_mission_missionType === void 0 ? void 0 : _event_mission_missionType.replaceAll(\"_\", \" \"),\n                    description: event === null || event === void 0 ? void 0 : (_event_mission4 = event.mission) === null || _event_mission4 === void 0 ? void 0 : _event_mission4.description,\n                    operationOutcome: event === null || event === void 0 ? void 0 : (_event_mission5 = event.mission) === null || _event_mission5 === void 0 ? void 0 : (_event_mission_operationOutcome = _event_mission5.operationOutcome) === null || _event_mission_operationOutcome === void 0 ? void 0 : _event_mission_operationOutcome.replaceAll(\"_\", \" \"),\n                    currentLocationID: event === null || event === void 0 ? void 0 : (_event_mission6 = event.mission) === null || _event_mission6 === void 0 ? void 0 : (_event_mission_currentLocation = _event_mission6.currentLocation) === null || _event_mission_currentLocation === void 0 ? void 0 : _event_mission_currentLocation.id,\n                    operationDescription: event === null || event === void 0 ? void 0 : (_event_mission7 = event.mission) === null || _event_mission7 === void 0 ? void 0 : _event_mission7.operationDescription,\n                    lat: event === null || event === void 0 ? void 0 : (_event_mission8 = event.mission) === null || _event_mission8 === void 0 ? void 0 : (_event_mission_currentLocation1 = _event_mission8.currentLocation) === null || _event_mission_currentLocation1 === void 0 ? void 0 : _event_mission_currentLocation1.lat,\n                    long: event === null || event === void 0 ? void 0 : (_event_mission9 = event.mission) === null || _event_mission9 === void 0 ? void 0 : (_event_mission_currentLocation2 = _event_mission9.currentLocation) === null || _event_mission_currentLocation2 === void 0 ? void 0 : _event_mission_currentLocation2.long\n                });\n                setTimeline(event === null || event === void 0 ? void 0 : (_event_missionTimeline = event.missionTimeline) === null || _event_missionTimeline === void 0 ? void 0 : _event_missionTimeline.nodes);\n                setCurrentLocation({\n                    latitude: event === null || event === void 0 ? void 0 : (_event_mission10 = event.mission) === null || _event_mission10 === void 0 ? void 0 : (_event_mission_currentLocation3 = _event_mission10.currentLocation) === null || _event_mission_currentLocation3 === void 0 ? void 0 : _event_mission_currentLocation3.lat,\n                    longitude: event === null || event === void 0 ? void 0 : (_event_mission11 = event.mission) === null || _event_mission11 === void 0 ? void 0 : (_event_mission_currentLocation4 = _event_mission11.currentLocation) === null || _event_mission_currentLocation4 === void 0 ? void 0 : _event_mission_currentLocation4.long\n                });\n                setCurrentMissionLocation({\n                    latitude: event === null || event === void 0 ? void 0 : event.lat,\n                    longitude: event === null || event === void 0 ? void 0 : event.long\n                });\n                setLocationDescription(event === null || event === void 0 ? void 0 : event.locationDescription);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    const handleSetMemberList = (members)=>{\n        var _members_filter;\n        setMembers(members === null || members === void 0 ? void 0 : (_members_filter = members.filter((member)=>member.archived == false && member.firstName != \"\")) === null || _members_filter === void 0 ? void 0 : _members_filter.map((member)=>({\n                label: member.firstName + \" \" + member.surname,\n                value: member.id\n            })));\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_6__.getSeaLogsMembersList)(handleSetMemberList, offline);\n    const handleCommentTimeChange = (date)=>{\n        setCommentTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    // setCommentTime(date)\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (geoLocations) {\n            setLocations(geoLocations.map((location)=>({\n                    label: location.title,\n                    value: location.id,\n                    latitude: location.lat,\n                    longitude: location.long\n                })));\n        }\n    }, [\n        geoLocations\n    ]);\n    const vesselTypes = [\n        {\n            label: \"Commercial\",\n            value: \"Commercial\"\n        },\n        {\n            label: \"Recreation\",\n            value: \"Recreation\"\n        },\n        // { label: 'Power', value: 'Power' },\n        {\n            label: \"Sail\",\n            value: \"Sail\"\n        },\n        {\n            label: \"Paddle crafts\",\n            value: \"Paddle crafts\"\n        },\n        {\n            label: \"PWC\",\n            value: \"PWC\"\n        },\n        {\n            label: \"Other\",\n            value: \"Other\"\n        }\n    ];\n    const missions = [\n        {\n            label: \"To locate\",\n            value: \"To locate\"\n        },\n        {\n            label: \"To assist\",\n            value: \"To assist\"\n        },\n        {\n            label: \"To save\",\n            value: \"To save\"\n        },\n        {\n            label: \"To rescue\",\n            value: \"To rescue\"\n        },\n        {\n            label: \"To remove\",\n            value: \"To remove\"\n        }\n    ];\n    const operationOutcomes = [\n        {\n            label: \"Assisted by others\",\n            value: \"Assisted by others\"\n        },\n        {\n            label: \"Assisted on scene\",\n            value: \"Assisted on scene\"\n        },\n        {\n            label: \"Medical treatment\",\n            value: \"Medical treatment\"\n        },\n        {\n            label: \"Safe and well\",\n            value: \"Safe and well\"\n        },\n        {\n            label: \"Not located\",\n            value: \"Not located\"\n        },\n        {\n            label: \"Not recoverable\",\n            value: \"Not recoverable\"\n        },\n        {\n            label: \"Fatality\",\n            value: \"Fatality\"\n        },\n        {\n            label: \"Stood down\",\n            value: \"Stood down\"\n        },\n        {\n            label: \"Other\",\n            value: \"Other\"\n        }\n    ];\n    const commentTypes = [\n        {\n            label: \"General\",\n            value: \"General\"\n        },\n        {\n            label: \"Underway\",\n            value: \"Underway\"\n        },\n        {\n            label: \"On Scene\",\n            value: \"On Scene\"\n        }\n    ];\n    const operationType = [\n        {\n            label: \"Mechanical / equipment failure\",\n            value: \"Mechanical / equipment failure\"\n        },\n        {\n            label: \"Vessel adrift\",\n            value: \"Vessel adrift\"\n        },\n        {\n            label: \"Vessel aground\",\n            value: \"Vessel aground\"\n        },\n        {\n            label: \"Capsize\",\n            value: \"Capsize\"\n        },\n        {\n            label: \"Vessel requiring tow\",\n            value: \"Vessel requiring tow\"\n        },\n        {\n            label: \"Flare sighting\",\n            value: \"Flare sighting\"\n        },\n        {\n            label: \"Vessel sinking\",\n            value: \"Vessel sinking\"\n        },\n        {\n            label: \"Collision\",\n            value: \"Collision\"\n        },\n        {\n            label: \"Vessel overdue\",\n            value: \"Vessel overdue\"\n        },\n        {\n            label: \"Other\",\n            value: \"Other\"\n        }\n    ];\n    const handleSaveComments = async ()=>{\n        if ((rescueData === null || rescueData === void 0 ? void 0 : rescueData.missionID) === undefined) {\n            sonner__WEBPACK_IMPORTED_MODULE_19__.toast.error(\"Please save the event first in order to create timeline!\");\n            setOpenCommentsDialog(false);\n            return;\n        }\n        const variables = {\n            input: {\n                commentType: (commentData === null || commentData === void 0 ? void 0 : commentData.commentType) ? commentData === null || commentData === void 0 ? void 0 : commentData.commentType : \"General\",\n                description: content ? content : \"\",\n                time: commentTime ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"DD/MM/YYYY\") + \" \" + commentTime : dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"DD/MM/YYYY HH:mm\"),\n                authorID: commentData === null || commentData === void 0 ? void 0 : commentData.authorID,\n                // missionID: rescueData?.missionID,\n                vesselRescueID: currentRescueID\n            }\n        };\n        if ((commentData === null || commentData === void 0 ? void 0 : commentData.id) > 0) {\n            if (offline) {\n                await missionTimelineModel.save({\n                    id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                    ...variables.input\n                });\n                // toast.success('Mission timeline updated')\n                setOpenCommentsDialog(false);\n                setDeleteCommentsDialog(false);\n                getCurrentEvent(currentRescueID);\n            } else {\n                updateMissionTimeline({\n                    variables: {\n                        input: {\n                            id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                await missionTimelineModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__.generateUniqueId)(),\n                    ...variables.input\n                });\n                // toast.success('Mission timeline created')\n                setOpenCommentsDialog(false);\n                setDeleteCommentsDialog(false);\n                await getCurrentEvent(currentRescueID);\n            } else {\n                createMissionTimeline({\n                    variables: {\n                        input: {\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        }\n        setOpenCommentsDialog(false);\n    };\n    const [createMissionTimeline] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateMissionTimeline, {\n        onCompleted: (response)=>{\n            // toast.success('Mission timeline created')\n            setOpenCommentsDialog(false);\n            setDeleteCommentsDialog(false);\n            getCurrentEvent(currentRescueID);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating mission timeline\", error);\n        }\n    });\n    const [updateMissionTimeline] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateMissionTimeline, {\n        onCompleted: (response)=>{\n            // toast.success('Mission timeline updated')\n            setOpenCommentsDialog(false);\n            setDeleteCommentsDialog(false);\n            getCurrentEvent(currentRescueID);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating mission timeline\", error);\n        }\n    });\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const handleSave = async ()=>{\n        var _rescueData_latitude, _currentLocation_latitude, _rescueData_longitude, _currentLocation_longitude, _rescueData_operationType;\n        const variables = {\n            input: {\n                vesselName: rescueData.vesselName,\n                callSign: rescueData.callSign,\n                pob: +rescueData.pob,\n                latitude: rescueData.latitude > 0 ? (_rescueData_latitude = rescueData.latitude) === null || _rescueData_latitude === void 0 ? void 0 : _rescueData_latitude.toString() : (_currentLocation_latitude = currentLocation.latitude) === null || _currentLocation_latitude === void 0 ? void 0 : _currentLocation_latitude.toString(),\n                longitude: rescueData.longitude > 0 ? (_rescueData_longitude = rescueData.longitude) === null || _rescueData_longitude === void 0 ? void 0 : _rescueData_longitude.toString() : (_currentLocation_longitude = currentLocation.longitude) === null || _currentLocation_longitude === void 0 ? void 0 : _currentLocation_longitude.toString(),\n                locationDescription: rescueData.locationDescription,\n                vesselLength: +rescueData.vesselLength,\n                vesselType: rescueData.vesselType,\n                makeAndModel: rescueData.makeAndModel,\n                color: rescueData.color,\n                ownerName: rescueData.ownerName,\n                phone: rescueData.phone,\n                email: rescueData.email,\n                address: rescueData.address,\n                ownerOnBoard: rescueData.ownerOnBoard,\n                cgMembershipType: \"cgnz\",\n                cgMembership: rescueData.cgMembership,\n                missionID: rescueData.missionID,\n                vesselLocationID: rescueData.locationID > 0 ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                operationType: (_rescueData_operationType = rescueData.operationType) === null || _rescueData_operationType === void 0 ? void 0 : _rescueData_operationType.map((type)=>type.value).join(\",\"),\n                operationDescription: rescueData.operationDescription,\n                vesselTypeDescription: rescueData.vesselTypeDescription\n            }\n        };\n        if (currentRescueID > 0) {\n            if (offline) {\n                const data = await vesselRescueModel.save({\n                    id: +currentRescueID,\n                    ...variables.input\n                });\n                if (rescueData.missionID > 0) {\n                    var _currentMissionLocation_latitude, _currentMissionLocation_longitude;\n                    await cgEventMissionModel.save({\n                        id: rescueData.missionID,\n                        missionType: missionData.missionType,\n                        description: missionData.description,\n                        operationDescription: missionData.operationDescription,\n                        operationOutcome: missionData.operationOutcome,\n                        completedAt: time,\n                        currentLocationID: rescueData.locationID,\n                        eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                        eventType: \"VesselRescue\",\n                        lat: (_currentMissionLocation_latitude = currentMissionLocation.latitude) === null || _currentMissionLocation_latitude === void 0 ? void 0 : _currentMissionLocation_latitude.toString(),\n                        long: (_currentMissionLocation_longitude = currentMissionLocation.longitude) === null || _currentMissionLocation_longitude === void 0 ? void 0 : _currentMissionLocation_longitude.toString()\n                    });\n                } else {\n                    var _currentMissionLocation_latitude1, _currentMissionLocation_longitude1;\n                    var _currentMissionLocation_latitude_toString, _currentMissionLocation_longitude_toString;\n                    await cgEventMissionModel.save({\n                        id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__.generateUniqueId)(),\n                        missionType: missionData.missionType,\n                        description: missionData.description,\n                        operationDescription: missionData.operationDescription,\n                        operationOutcome: missionData.operationOutcome,\n                        completedAt: time,\n                        currentLocationID: rescueData.currentLocationID,\n                        eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                        eventType: \"VesselRescue\",\n                        lat: (_currentMissionLocation_latitude_toString = (_currentMissionLocation_latitude1 = currentMissionLocation.latitude) === null || _currentMissionLocation_latitude1 === void 0 ? void 0 : _currentMissionLocation_latitude1.toString()) !== null && _currentMissionLocation_latitude_toString !== void 0 ? _currentMissionLocation_latitude_toString : null,\n                        long: (_currentMissionLocation_longitude_toString = (_currentMissionLocation_longitude1 = currentMissionLocation.longitude) === null || _currentMissionLocation_longitude1 === void 0 ? void 0 : _currentMissionLocation_longitude1.toString()) !== null && _currentMissionLocation_longitude_toString !== void 0 ? _currentMissionLocation_longitude_toString : null\n                    });\n                }\n                handleSaveParent(+currentRescueID, 0);\n            } else {\n                updateEventType_VesselRescue({\n                    variables: {\n                        input: {\n                            id: +currentRescueID,\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                var _rescueData_latitude1, _currentLocation_latitude1, _rescueData_longitude1, _currentLocation_longitude1, _rescueData_operationType1;\n                const data = await vesselRescueModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__.generateUniqueId)(),\n                    vesselName: rescueData.vesselName,\n                    callSign: rescueData.callSign,\n                    pob: +rescueData.pob,\n                    latitude: rescueData.latitude > 0 ? (_rescueData_latitude1 = rescueData.latitude) === null || _rescueData_latitude1 === void 0 ? void 0 : _rescueData_latitude1.toString() : (_currentLocation_latitude1 = currentLocation.latitude) === null || _currentLocation_latitude1 === void 0 ? void 0 : _currentLocation_latitude1.toString(),\n                    longitude: rescueData.longitude > 0 ? (_rescueData_longitude1 = rescueData.longitude) === null || _rescueData_longitude1 === void 0 ? void 0 : _rescueData_longitude1.toString() : (_currentLocation_longitude1 = currentLocation.longitude) === null || _currentLocation_longitude1 === void 0 ? void 0 : _currentLocation_longitude1.toString(),\n                    locationDescription: rescueData.locationDescription,\n                    vesselLength: +rescueData.vesselLength,\n                    vesselType: rescueData.vesselType,\n                    makeAndModel: rescueData.makeAndModel,\n                    color: rescueData.color,\n                    ownerName: rescueData.ownerName,\n                    phone: rescueData.phone,\n                    email: rescueData.email,\n                    address: rescueData.address,\n                    ownerOnBoard: rescueData.ownerOnBoard,\n                    cgMembershipType: \"cgnz\",\n                    cgMembership: rescueData.cgMembership,\n                    missionID: rescueData.missionID,\n                    vesselLocationID: rescueData.locationID ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                    operationType: (_rescueData_operationType1 = rescueData.operationType) === null || _rescueData_operationType1 === void 0 ? void 0 : _rescueData_operationType1.map((type)=>type.value).join(\",\"),\n                    operationDescription: rescueData.operationDescription,\n                    vesselTypeDescription: rescueData.vesselTypeDescription\n                });\n                await cgEventMissionModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__.generateUniqueId)(),\n                    missionType: missionData.missionType,\n                    description: missionData.description,\n                    operationDescription: missionData.operationDescription,\n                    operationOutcome: missionData.operationOutcome,\n                    completedAt: time,\n                    currentLocationID: rescueData.locationID ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                    eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                    eventType: \"VesselRescue\"\n                });\n                handleSaveParent(+(data === null || data === void 0 ? void 0 : data.id), 0);\n                closeModal();\n            } else {\n                var _rescueData_latitude2, _currentLocation_latitude2, _rescueData_longitude2, _currentLocation_longitude2, _rescueData_operationType2;\n                createEventType_VesselRescue({\n                    variables: {\n                        input: {\n                            vesselName: rescueData.vesselName,\n                            callSign: rescueData.callSign,\n                            pob: +rescueData.pob,\n                            latitude: rescueData.latitude > 0 ? (_rescueData_latitude2 = rescueData.latitude) === null || _rescueData_latitude2 === void 0 ? void 0 : _rescueData_latitude2.toString() : (_currentLocation_latitude2 = currentLocation.latitude) === null || _currentLocation_latitude2 === void 0 ? void 0 : _currentLocation_latitude2.toString(),\n                            longitude: rescueData.longitude > 0 ? (_rescueData_longitude2 = rescueData.longitude) === null || _rescueData_longitude2 === void 0 ? void 0 : _rescueData_longitude2.toString() : (_currentLocation_longitude2 = currentLocation.longitude) === null || _currentLocation_longitude2 === void 0 ? void 0 : _currentLocation_longitude2.toString(),\n                            locationDescription: rescueData.locationDescription,\n                            vesselLength: +rescueData.vesselLength,\n                            vesselType: rescueData.vesselType,\n                            makeAndModel: rescueData.makeAndModel,\n                            color: rescueData.color,\n                            ownerName: rescueData.ownerName,\n                            phone: rescueData.phone,\n                            email: rescueData.email,\n                            address: rescueData.address,\n                            ownerOnBoard: rescueData.ownerOnBoard,\n                            cgMembershipType: \"cgnz\",\n                            cgMembership: rescueData.cgMembership,\n                            missionID: rescueData.missionID,\n                            vesselLocationID: rescueData.locationID ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                            operationType: (_rescueData_operationType2 = rescueData.operationType) === null || _rescueData_operationType2 === void 0 ? void 0 : _rescueData_operationType2.map((type)=>type.value).join(\",\"),\n                            operationDescription: rescueData.operationDescription,\n                            vesselTypeDescription: rescueData.vesselTypeDescription\n                        }\n                    }\n                });\n            }\n        }\n    };\n    // Expose the save function to parent component via ref\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, ()=>({\n            save: handleSave\n        }), [\n        handleSave\n    ]);\n    const [createEventType_VesselRescue] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateEventType_VesselRescue, {\n        onCompleted: (response)=>{\n            const data = response.createEventType_VesselRescue;\n            createCGEventMission({\n                variables: {\n                    input: {\n                        missionType: missionData.missionType,\n                        description: missionData.description,\n                        operationDescription: missionData.operationDescription,\n                        operationOutcome: missionData.operationOutcome,\n                        completedAt: time,\n                        currentLocationID: rescueData.locationID ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                        eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                        eventType: \"VesselRescue\"\n                    }\n                }\n            });\n            handleSaveParent(+(data === null || data === void 0 ? void 0 : data.id), 0);\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating vessel rescue\", error);\n        }\n    });\n    const [updateEventType_VesselRescue] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateEventType_VesselRescue, {\n        onCompleted: (response)=>{\n            const data = response.updateEventType_VesselRescue;\n            if (rescueData.missionID > 0) {\n                var _currentMissionLocation_latitude, _currentMissionLocation_longitude;\n                updateCGEventMission({\n                    variables: {\n                        input: {\n                            id: rescueData.missionID,\n                            missionType: missionData.missionType,\n                            description: missionData.description,\n                            operationDescription: missionData.operationDescription,\n                            operationOutcome: missionData.operationOutcome,\n                            completedAt: time,\n                            currentLocationID: rescueData.locationID,\n                            eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                            eventType: \"VesselRescue\",\n                            lat: (_currentMissionLocation_latitude = currentMissionLocation.latitude) === null || _currentMissionLocation_latitude === void 0 ? void 0 : _currentMissionLocation_latitude.toString(),\n                            long: (_currentMissionLocation_longitude = currentMissionLocation.longitude) === null || _currentMissionLocation_longitude === void 0 ? void 0 : _currentMissionLocation_longitude.toString()\n                        }\n                    }\n                });\n            } else {\n                createCGEventMission({\n                    variables: {\n                        input: {\n                            missionType: missionData.missionType,\n                            description: missionData.description,\n                            operationDescription: missionData.operationDescription,\n                            operationOutcome: missionData.operationOutcome,\n                            completedAt: time,\n                            currentLocationID: rescueData.currentLocationID,\n                            eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                            eventType: \"VesselRescue\",\n                            lat: currentMissionLocation.latitude.toString(),\n                            long: currentMissionLocation.longitude.toString()\n                        }\n                    }\n                });\n            }\n            handleSaveParent(+currentRescueID, 0);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating vessel rescue\", error);\n        }\n    });\n    const [createCGEventMission] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateCGEventMission, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"Error creating CG Event Mission\", error);\n        }\n    });\n    const [updateCGEventMission] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateCGEventMission, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"Error updating CG Event Mission\", error);\n        }\n    });\n    const handleLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setRescueData((prev)=>{\n                return {\n                    ...prev,\n                    locationID: +value.value,\n                    latitude: null,\n                    longitude: null\n                };\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setRescueData((prev)=>{\n                return {\n                    ...prev,\n                    locationID: 0,\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                };\n            });\n        }\n    };\n    const handleMissionLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setMissionData({\n                ...missionData,\n                currentLocationID: +value.value,\n                lat: null,\n                long: null\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setMissionData({\n                ...missionData,\n                currentLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n        }\n    };\n    const handleCreateComment = ()=>{\n        if (selectedEvent) {\n            setOpenCommentsDialog(true);\n            handleEditorChange(\"\");\n            setCommentData(false);\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_19__.toast.error(\"Please save the event first in order to create timeline!\");\n        }\n    };\n    const handleEditComment = (comment)=>{\n        setOpenCommentsDialog(true);\n        setCommentData(comment);\n        handleEditorChange(comment.description);\n        setCommentTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(comment.time).format(\"HH:mm\"));\n    };\n    const handleDeleteComment = (commentId)=>{\n        const comment = timeline === null || timeline === void 0 ? void 0 : timeline.find((c)=>c.id === commentId);\n        if (comment) {\n            setDeleteCommentsDialog(true);\n            setCommentData(comment);\n        }\n    };\n    const handleDeleteComments = async ()=>{\n        if (offline) {\n            await missionTimelineModel.save({\n                id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                archived: true\n            });\n            setOpenCommentsDialog(false);\n            setDeleteCommentsDialog(false);\n            getCurrentEvent(currentRescueID);\n            setDeleteCommentsDialog(false);\n        } else {\n            updateMissionTimeline({\n                variables: {\n                    input: {\n                        id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                        archived: true\n                    }\n                }\n            });\n            setDeleteCommentsDialog(false);\n        }\n    };\n    const offlineGetSeaLogsMembersList = async ()=>{\n        // getSeaLogsMembersList(handleSetMemberList)\n        const members = await memberModel.getAll();\n        handleSetMemberList(members);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (offline) {\n            offlineGetSeaLogsMembersList();\n        }\n    }, [\n        offline\n    ]);\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_6__.getVesselByID)(+vesselID, setVessel, offline);\n    const [getSectionCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            let data = response.readCrewMembers_LogBookEntrySections.nodes;\n            setCrewMembers(data);\n        },\n        onError: (error)=>{\n            console.error(\"CrewMembers_LogBookEntrySection error\", error);\n        }\n    });\n    const handleSetLogbook = async (logbook)=>{\n        setLogbook(logbook);\n        const sectionTypes = Array.from(new Set(logbook.logBookEntrySections.nodes.map((sec)=>sec.className))).map((type)=>({\n                className: type,\n                ids: logbook.logBookEntrySections.nodes.filter((sec)=>sec.className === type).map((sec)=>sec.id)\n            }));\n        sectionTypes.forEach(async (section)=>{\n            if (section.className === \"SeaLogs\\\\CrewMembers_LogBookEntrySection\") {\n                if (offline) {\n                    const data = await cmlbsModel.getByIds(section.ids);\n                    setCrewMembers(data);\n                } else {\n                    const searchFilter = {};\n                    searchFilter.id = {\n                        in: section.ids\n                    };\n                    getSectionCrewMembers_LogBookEntrySection({\n                        variables: {\n                            filter: searchFilter\n                        }\n                    });\n                }\n            }\n        });\n    };\n    const getLogBookEntryByID = async (id)=>{\n        if (offline) {\n            const data = await logbookModel.getById(id);\n            if (data) {\n                handleSetLogbook(data);\n            }\n        } else {\n            queryLogBookEntry({\n                variables: {\n                    logbookEntryId: +id\n                }\n            });\n        }\n    };\n    const [queryLogBookEntry] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GET_LOGBOOK_ENTRY_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneLogBookEntry;\n            if (data) {\n                handleSetLogbook(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryLogBookEntry error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        getLogBookEntryByID(+logentryID);\n    }, []);\n    const [queryVesselCrews] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CREW_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers;\n            if (data) {\n                const allMembers = data.nodes.filter((item)=>{\n                    return +item.id !== +masterID;\n                }).map((member)=>{\n                    // const crewWithTraining = GetCrewListWithTrainingStatus(\n                    //     [member],\n                    //     [vessel],\n                    // )[0]\n                    return {\n                        label: \"\".concat(member.firstName || \"\", \" \").concat(member.surname || \"\").trim(),\n                        value: member.id,\n                        // data: crewWithTraining,\n                        profile: {\n                            firstName: member.firstName,\n                            surname: member.surname,\n                            avatar: member.profileImage\n                        }\n                    };\n                });\n                setAllMembers(allMembers);\n                const members = allMembers.filter((member)=>{\n                    if (!crewMembers) {\n                        return true;\n                    }\n                    return !Array.isArray(crewMembers) || !crewMembers.some((section)=>section && section.crewMember && section.crewMember.id === member.value && section.punchOut === null);\n                });\n                const memberOptions = members.filter((member)=>!crewMembersList || !Array.isArray(crewMembersList) || !crewMembersList.includes(+member.value));\n                setCrewMemberOptions(memberOptions);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVesselCrews error\", error);\n        }\n    });\n    const loadVesselCrews = async ()=>{\n        if (offline) {\n            const data = await seaLogsMemberModel.getByVesselId(vesselID);\n            setAllVesselCrews(data);\n            if (data) {\n                const members = data.filter((item)=>+item.id !== +logbook.master.id).map((member)=>{\n                    const crewWithTraining = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_6__.GetCrewListWithTrainingStatus)([\n                        member\n                    ], [\n                        vessel\n                    ])[0];\n                    return {\n                        label: \"\".concat(member.firstName || \"\", \" \").concat(member.surname || \"\").trim(),\n                        value: member.id,\n                        data: crewWithTraining,\n                        profile: {\n                            firstName: member.firstName,\n                            surname: member.surname,\n                            avatar: member.profileImage\n                        }\n                    };\n                });\n                setCrewMemberOptions(members);\n            }\n        } else {\n            await queryVesselCrews({\n                variables: {\n                    filter: {\n                        vehicles: {\n                            id: {\n                                eq: vesselID\n                            }\n                        },\n                        isArchived: {\n                            eq: false\n                        }\n                    }\n                }\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isLoading) {\n            loadVesselCrews();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" pt-0\"),\n        children: [\n            type === \"TaskingComplete\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-3 gap-6 pb-0 pt-0 px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-0  col-span-3 md:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_21__.H3, {\n                                    children: \"Mission complete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1162,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_21__.P, {\n                                    children: \"Record the operation outcome, location and time of completion\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1163,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1161,\n                            columnNumber: 29\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-3 md:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__.Combobox, {\n                                        options: operationOutcomes,\n                                        value: operationOutcomes === null || operationOutcomes === void 0 ? void 0 : operationOutcomes.find((outcome)=>outcome.value == (missionData === null || missionData === void 0 ? void 0 : missionData.operationOutcome)),\n                                        onChange: (value)=>{\n                                            setMissionData({\n                                                ...missionData,\n                                                operationOutcome: value === null || value === void 0 ? void 0 : value.value\n                                            });\n                                        },\n                                        placeholder: \"Operation outcome\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1170,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1169,\n                                    columnNumber: 33\n                                }, undefined),\n                                (missionData === null || missionData === void 0 ? void 0 : missionData.operationOutcome) == \"Other\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                                        id: \"operation-outcome-description\",\n                                        rows: 4,\n                                        className: \"\",\n                                        placeholder: \"Description\",\n                                        value: (missionData === null || missionData === void 0 ? void 0 : missionData.operationDescription) ? missionData === null || missionData === void 0 ? void 0 : missionData.operationDescription : \"\",\n                                        onChange: ()=>{\n                                            setMissionData({\n                                                ...missionData,\n                                                operationDescription: document.getElementById(\"operation-outcome-description\").value\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1188,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1187,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1168,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                    lineNumber: 1160,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 pb-4 pt-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2.5\",\n                        children: [\n                            timeline && (timeline === null || timeline === void 0 ? void 0 : timeline.map((comment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_task_task__WEBPACK_IMPORTED_MODULE_27__.RecordCard, {\n                                    record: comment,\n                                    onEdit: handleEditComment,\n                                    onDelete: handleDeleteComment\n                                }, \"\".concat(comment.id, \"-record-\").concat(comment.time || \"\"), false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1220,\n                                    columnNumber: 37\n                                }, undefined))),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                                        onClick: ()=>setShowInputDetailsPanel(true),\n                                        children: \"Vessel details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1228,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Tooltip, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.TooltipTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                                                    onClick: handleCreateComment,\n                                                    disabled: !selectedEvent,\n                                                    children: \"Add notes/comments\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                    lineNumber: 1237,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1236,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.TooltipContent, {\n                                                hidden: (selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id) > 0,\n                                                children: \"Please save the event first in order to create timeline!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1243,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1235,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                lineNumber: 1227,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                        lineNumber: 1217,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                    lineNumber: 1216,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1215,\n                columnNumber: 17\n            }, undefined),\n            showSaveButtons && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n                        onClick: ()=>closeModal(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                        lineNumber: 1255,\n                        columnNumber: 25\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                        onClick: handleSave,\n                        children: \"Save\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                        lineNumber: 1261,\n                        columnNumber: 25\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1254,\n                columnNumber: 21\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_18__.AlertDialogNew, {\n                openDialog: openCommentsDialog,\n                setOpenDialog: setOpenCommentsDialog,\n                size: \"xl\",\n                handleCreate: handleSaveComments,\n                title: (commentData === null || commentData === void 0 ? void 0 : commentData.id) > 0 ? \"Update Comment\" : \"Create Comment\",\n                actionText: (commentData === null || commentData === void 0 ? void 0 : commentData.id) > 0 ? \"Update\" : \"Create Comment\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                            htmlFor: \"comment-type\",\n                            label: \"Comment Type\",\n                            className: \"text-sm font-medium\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__.Combobox, {\n                                id: \"comment-type\",\n                                options: commentTypes,\n                                value: commentTypes === null || commentTypes === void 0 ? void 0 : commentTypes.find((type)=>{\n                                    var _commentData_commentType;\n                                    return type.value == (commentData === null || commentData === void 0 ? void 0 : (_commentData_commentType = commentData.commentType) === null || _commentData_commentType === void 0 ? void 0 : _commentData_commentType.replaceAll(\"_\", \" \"));\n                                }),\n                                onChange: (value)=>setCommentData({\n                                        ...commentData,\n                                        commentType: value === null || value === void 0 ? void 0 : value.value\n                                    }),\n                                placeholder: \"Select comment type\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                lineNumber: 1282,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1278,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                    htmlFor: \"comment_time\",\n                                    className: \"flex items-center gap-2\",\n                                    children: \"Time of Completion\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1304,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    time: commentTime,\n                                    handleTimeChange: (date)=>{\n                                        handleCommentTimeChange(date);\n                                    },\n                                    timeID: \"comment_time\",\n                                    fieldName: \"comment_time\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1309,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1303,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                    htmlFor: \"comment\",\n                                    children: \"Comment Content\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1320,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    id: \"comment\",\n                                    placeholder: \"Write your comment here...\",\n                                    className: \"w-full min-h-[150px] bg-secondary-foreground\",\n                                    content: content,\n                                    handleEditorChange: handleEditorChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1321,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1319,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                    htmlFor: \"author\",\n                                    className: \"flex items-center gap-2\",\n                                    children: \"Author\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1331,\n                                    columnNumber: 29\n                                }, undefined),\n                                members && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__.Combobox, {\n                                    id: \"author\",\n                                    options: crewMemberOptions,\n                                    value: crewMemberOptions === null || crewMemberOptions === void 0 ? void 0 : crewMemberOptions.find((member)=>{\n                                        var _commentData_author;\n                                        return member.value == (commentData === null || commentData === void 0 ? void 0 : (_commentData_author = commentData.author) === null || _commentData_author === void 0 ? void 0 : _commentData_author.id);\n                                    }),\n                                    onChange: (value)=>setCommentData({\n                                            ...commentData,\n                                            authorID: value === null || value === void 0 ? void 0 : value.value\n                                        }),\n                                    placeholder: \"Select crew\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1337,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1330,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                    lineNumber: 1277,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1264,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_18__.AlertDialogNew, {\n                openDialog: deleteCommentsDialog,\n                setOpenDialog: setDeleteCommentsDialog,\n                handleCreate: handleDeleteComments,\n                title: \"Delete Comment\",\n                variant: \"warning\",\n                actionText: \"Confirm delete\",\n                children: \"Are you sure you want to delete this comment? This action cannot be undone and all associated data will be permanently removed from the system.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1357,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_20__.Sheet, {\n                open: showInputDetailsP,\n                onOpenChange: (open)=>setShowInputDetailsPanel(open),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_20__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-full max-w-md sm:max-w-xl bg-background phablet:bg-muted\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_20__.SheetHeader, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1374,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col h-full min-h-[400px] overflow-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-grow space-y-6 py-4 mx-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.CardHeader, {\n                                                        className: \"pb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"my-4 text-sm font-semibold uppercase\",\n                                                                    children: \"Target Vessel Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                    lineNumber: 1382,\n                                                                    columnNumber: 49\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1381,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_21__.P, {\n                                                                children: \"Record vessel name, callsign and number of people on board\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1386,\n                                                                columnNumber: 45\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1380,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.CardContent, {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"vessel-name\",\n                                                                        children: \"Vessel Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1393,\n                                                                        columnNumber: 49\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                        id: \"vessel-name\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Enter vessel name\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselName) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                vesselName: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1396,\n                                                                        columnNumber: 49\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1392,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"call-sign\",\n                                                                        children: \"Call Sign\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1415,\n                                                                        columnNumber: 49\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                        id: \"call-sign\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Enter call sign\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.callSign) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                callSign: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1418,\n                                                                        columnNumber: 49\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1414,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"pob\",\n                                                                        children: \"People On Board (POB)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1437,\n                                                                        columnNumber: 49\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                        id: \"pob\",\n                                                                        type: \"number\",\n                                                                        placeholder: \"Enter number of people\",\n                                                                        min: 1,\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.pob) || 0,\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                pob: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1440,\n                                                                        columnNumber: 49\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1436,\n                                                                columnNumber: 45\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1391,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1379,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.CardHeader, {\n                                                        className: \"pb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"my-4 text-sm font-semibold uppercase\",\n                                                                    children: \"Vessel Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                    lineNumber: 1461,\n                                                                    columnNumber: 49\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1460,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_21__.P, {\n                                                                children: \"Include details of vessel type, make and descriptors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1465,\n                                                                columnNumber: 45\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1459,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.CardContent, {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"vessel-length\",\n                                                                        children: \"Number of Vessels\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1472,\n                                                                        columnNumber: 49\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                        id: \"vessel-length\",\n                                                                        type: \"number\",\n                                                                        placeholder: \"Enter vessel length\",\n                                                                        min: 1,\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselLength) || 0,\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                vesselLength: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1475,\n                                                                        columnNumber: 49\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1471,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"vessel-type\",\n                                                                        children: \"Vessel type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1495,\n                                                                        columnNumber: 49\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_16__.Combobox, {\n                                                                        options: vesselTypes,\n                                                                        value: vesselTypes === null || vesselTypes === void 0 ? void 0 : vesselTypes.find((type)=>type.value == (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselType)),\n                                                                        onChange: (value)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                vesselType: value === null || value === void 0 ? void 0 : value.value\n                                                                            });\n                                                                        },\n                                                                        placeholder: \"Select vessel type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1498,\n                                                                        columnNumber: 49\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1494,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselType) == \"Other\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"vessel-type-description\",\n                                                                        children: \"Vessel type description\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1519,\n                                                                        columnNumber: 53\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                                                                        id: \"vessel-type-description\",\n                                                                        rows: 3,\n                                                                        placeholder: \"Describe the vessel type\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselTypeDescription) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                vesselTypeDescription: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1522,\n                                                                        columnNumber: 53\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1518,\n                                                                columnNumber: 49\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"make\",\n                                                                        children: \"Make and odel\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1543,\n                                                                        columnNumber: 49\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                        id: \"make\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Enter make and model\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.makeAndModel) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                makeAndModel: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1546,\n                                                                        columnNumber: 49\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1542,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"color\",\n                                                                        children: \"Color\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1565,\n                                                                        columnNumber: 49\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                        id: \"color\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Enter vessel color\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.color) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                color: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1568,\n                                                                        columnNumber: 49\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1564,\n                                                                columnNumber: 45\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1470,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1458,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.CardHeader, {\n                                                        className: \"pb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"my-4 text-sm font-semibold uppercase\",\n                                                                    children: \"Owner's Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                    lineNumber: 1591,\n                                                                    columnNumber: 49\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1590,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_21__.P, {\n                                                                children: \"Record vessel owner's details and membership number if applicable\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1595,\n                                                                columnNumber: 45\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1589,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_22__.CardContent, {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                                htmlFor: \"owner-name\",\n                                                                                children: \"Owner's Name\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1604,\n                                                                                columnNumber: 53\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                                id: \"owner-name\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter owner's name\",\n                                                                                value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.ownerName) || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    setRescueData({\n                                                                                        ...rescueData,\n                                                                                        ownerName: e.target.value\n                                                                                    });\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1607,\n                                                                                columnNumber: 53\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1603,\n                                                                        columnNumber: 49\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                                htmlFor: \"owner-phone\",\n                                                                                children: \"Phone Number\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1627,\n                                                                                columnNumber: 53\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                                id: \"owner-phone\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter phone number\",\n                                                                                value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.phone) || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    setRescueData({\n                                                                                        ...rescueData,\n                                                                                        phone: e.target.value\n                                                                                    });\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1630,\n                                                                                columnNumber: 53\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1626,\n                                                                        columnNumber: 49\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1602,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                                htmlFor: \"cgnz\",\n                                                                                children: \"Coastguard NZ Membership\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1651,\n                                                                                columnNumber: 53\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                                id: \"cgnz\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter membership number\",\n                                                                                value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.cgMembership) || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    setRescueData({\n                                                                                        ...rescueData,\n                                                                                        cgMembership: e.target.value\n                                                                                    });\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1654,\n                                                                                columnNumber: 53\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1650,\n                                                                        columnNumber: 49\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                                htmlFor: \"owner-email\",\n                                                                                children: \"Email Address\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1674,\n                                                                                columnNumber: 53\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                                id: \"owner-email\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter email address\",\n                                                                                value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.email) || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    setRescueData({\n                                                                                        ...rescueData,\n                                                                                        email: e.target.value\n                                                                                    });\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1677,\n                                                                                columnNumber: 53\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1673,\n                                                                        columnNumber: 49\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1649,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                        htmlFor: \"owner-address\",\n                                                                        children: \"Owner's Address\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1697,\n                                                                        columnNumber: 49\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                                                                        id: \"owner-address\",\n                                                                        rows: 3,\n                                                                        placeholder: \"Enter owner's address\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.address) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                address: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1700,\n                                                                        columnNumber: 49\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1696,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 pt-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                                    htmlFor: \"owner-onboard\",\n                                                                    className: \"cursor-pointer\",\n                                                                    label: \"Is the owner on-board?\",\n                                                                    leftContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_23__.Checkbox, {\n                                                                        id: \"owner-onboard\",\n                                                                        checked: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.ownerOnBoard) || false,\n                                                                        size: \"lg\",\n                                                                        isRadioStyle: true,\n                                                                        onCheckedChange: (checked)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                ownerOnBoard: checked === true\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1724,\n                                                                        columnNumber: 57\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                    lineNumber: 1719,\n                                                                    columnNumber: 49\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1718,\n                                                                columnNumber: 45\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1601,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1588,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1377,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1376,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                                        onClick: ()=>setShowInputDetailsPanel(false),\n                                        children: \"Save Changes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1753,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1751,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1375,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                    lineNumber: 1371,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1368,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n        lineNumber: 1157,\n        columnNumber: 13\n    }, undefined);\n}, \"YXSWLqFRv+CZTrMdxzOVwA0Rzbg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_28__.useMediaQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery\n    ];\n})), \"YXSWLqFRv+CZTrMdxzOVwA0Rzbg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_28__.useMediaQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery\n    ];\n});\n_c1 = VesselRescueFields;\nVesselRescueFields.displayName = \"VesselRescueFields\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (VesselRescueFields);\nvar _c, _c1;\n$RefreshReg$(_c, \"VesselRescueFields$forwardRef\");\n$RefreshReg$(_c1, \"VesselRescueFields\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvbG9nYm9vay9mb3Jtcy92ZXNzZWwtcmVzY3VlLWZpZWxkcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUV5QjtBQU1YO0FBQ21DO0FBUWQ7QUFNSDtBQUtOO0FBQ087QUFDeUI7QUFDaEI7QUFFeUI7QUFDa0I7QUFDaEI7QUFDRTtBQUNMO0FBQ25CO0FBQ0Y7QUFDQTtBQUNNO0FBQ0E7QUFFYztBQUN6QjtBQUNWO0FBTUE7QUFDb0I7QUFPckI7QUFDc0I7QUFFYztBQUNzQztBQUN0QztBQU96QztBQUNtQztBQUUzRCxNQUFNc0QsbUNBQXFCbEQsR0FBQUEsaURBQVVBLFNBaUJqQyxRQWVJbUQ7UUFkQSxFQUNJQyxZQUFZLEVBQ1pDLGdCQUFnQixLQUFLLEVBQ3JCQyxVQUFVLEVBQ1ZDLGdCQUFnQixFQUNoQkMsZUFBZSxFQUNmQyxJQUFJLEVBQ0pDLG9CQUFvQixFQUNwQkMsbUJBQW1CLEVBQ25CQyxzQkFBc0IsRUFDdEJDLFVBQVUsS0FBSyxFQUNmQyxTQUFTLEtBQUssRUFDZEMsa0JBQWtCLElBQUksRUFDekI7O0lBR0QsTUFBTUMsZUFBZTlELGdFQUFlQTtJQUNwQyxNQUFNLENBQUMrRCxXQUFXQyxhQUFhLEdBQUduRSwrQ0FBUUEsQ0FBTTtJQUNoRCxNQUFNLENBQUNvRSxNQUFNQyxRQUFRLEdBQUdyRSwrQ0FBUUEsQ0FBTUgsNENBQUtBLEdBQUd5RSxNQUFNLENBQUM7SUFDckQsTUFBTSxDQUFDQyxvQkFBb0JDLHNCQUFzQixHQUFHeEUsK0NBQVFBLENBQUM7SUFDN0QsTUFBTSxDQUFDeUUsYUFBYUMsZUFBZSxHQUFHMUUsK0NBQVFBO0lBQzlDLE1BQU0sQ0FBQzJFLFNBQVNDLFdBQVcsR0FBRzVFLCtDQUFRQSxDQUFNO0lBQzVDLE1BQU0sQ0FBQzZFLFNBQVNDLFdBQVcsR0FBRzlFLCtDQUFRQTtJQUN0QyxNQUFNLENBQUMrRSxZQUFZQyxjQUFjLEdBQUdoRiwrQ0FBUUEsQ0FBTTtJQUNsRCxNQUFNLENBQUNpRixhQUFhQyxlQUFlLEdBQUdsRiwrQ0FBUUEsQ0FBTTtJQUNwRCxNQUFNLENBQUNtRixhQUFhQyxlQUFlLEdBQUdwRiwrQ0FBUUEsQ0FBTTtJQUNwRCxNQUFNLENBQUNxRixVQUFVQyxZQUFZLEdBQUd0RiwrQ0FBUUEsQ0FBTTtJQUM5QyxNQUFNLENBQUN1RixzQkFBc0JDLHdCQUF3QixHQUFHeEYsK0NBQVFBLENBQUM7SUFDakUsTUFBTSxDQUFDeUYsZ0JBQWdCQyxrQkFBa0IsR0FBRzFGLCtDQUFRQSxDQUFlLEVBQUU7SUFDckUsTUFBTSxDQUFDMkYsUUFBUUMsVUFBVSxHQUFHNUYsK0NBQVFBO0lBQ3BDLE1BQU0sQ0FBQzZGLFVBQVVDLFlBQVksR0FBRzlGLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQytGLG1CQUFtQkMscUJBQXFCLEdBQUdoRywrQ0FBUUEsQ0FBTSxFQUFFO0lBQ2xFLE1BQU0sQ0FBQ2lHLFlBQVlDLGNBQWMsR0FBR2xHLCtDQUFRQSxDQUFNLEVBQUU7SUFDcEQsTUFBTSxDQUFDbUcsYUFBYUMsZUFBZSxHQUFHcEcsK0NBQVFBLENBQU07SUFDcEQsTUFBTSxDQUFDcUcsaUJBQWlCQyxtQkFBbUIsR0FBR3RHLCtDQUFRQSxDQUFNLEVBQUU7SUFDOUQsTUFBTSxDQUFDdUcsU0FBU0MsV0FBVyxHQUFHeEcsK0NBQVFBLENBQU07SUFDNUMsTUFBTSxDQUFDeUcsaUJBQWlCQyxtQkFBbUIsR0FBRzFHLCtDQUFRQSxDQUFNO1FBQ3hEMkcsVUFBVTtRQUNWQyxXQUFXO0lBQ2Y7SUFDQSxNQUFNLENBQUNDLHdCQUF3QkMsMEJBQTBCLEdBQ3JEOUcsK0NBQVFBLENBQU07UUFDVjJHLFVBQVU7UUFDVkMsV0FBVztJQUNmO0lBQ0osTUFBTSxDQUFDRyxtQkFBbUJDLHlCQUF5QixHQUFHaEgsK0NBQVFBLENBQUM7SUFDL0QsTUFBTWlILFNBQVN4RiwrREFBYUEsQ0FBQztJQUU3QixNQUFNeUYsY0FBYyxJQUFJOUYseUVBQWtCQTtJQUMxQyxNQUFNK0Ysb0JBQW9CLElBQUk5RixtRkFBMkJBO0lBQ3pELE1BQU0rRixzQkFBc0IsSUFBSTlGLDJFQUFtQkE7SUFDbkQsTUFBTStGLHVCQUF1QixJQUFJOUYsNEVBQW9CQTtJQUNyRCxNQUFNK0YscUJBQXFCLElBQUlsRyx5RUFBa0JBO0lBQ2pELE1BQU1tRyxhQUFhLElBQUk1RSw0RkFBb0NBO0lBQzNELE1BQU02RSxlQUFlLElBQUk1RSx5RUFBaUJBO0lBQzFDLE1BQU02RSxtQkFBbUIsQ0FBQ0M7UUFDdEJyRCxRQUFReEUsNENBQUtBLENBQUM2SCxNQUFNcEQsTUFBTSxDQUFDO0lBQy9CO1FBQ2lCTDtJQUFqQixNQUFNMEQsV0FBVzFELENBQUFBLG9CQUFBQSxhQUFhMkQsR0FBRyxDQUFDLHlCQUFqQjNELCtCQUFBQSxvQkFBZ0M7UUFDOUJBO0lBQW5CLE1BQU00RCxhQUFhNUQsQ0FBQUEscUJBQUFBLGFBQWEyRCxHQUFHLENBQUMsMkJBQWpCM0QsZ0NBQUFBLHFCQUFrQztJQUNyRCxNQUFNLENBQUM2RCxXQUFXQyxhQUFhLEdBQUcvSCwrQ0FBUUEsQ0FBQztJQUUzQ0QsZ0RBQVNBLENBQUM7UUFDTmlGLGNBQWM7UUFDZCxJQUFJdkIsaUJBQWlCO1lBQ2pCdUUsZ0JBQWdCdkU7UUFDcEI7SUFDSixHQUFHO1FBQUNBO0tBQWdCO0lBRXBCMUQsZ0RBQVNBLENBQUM7UUFDTjJHLG1CQUFtQi9DLGlDQUFBQSwyQ0FBQUEscUJBQXNCOEMsZUFBZTtRQUN4RHdCLHFCQUFxQjtZQUFFQyxLQUFLLEVBQUV2RSxpQ0FBQUEsMkNBQUFBLHFCQUFzQndFLGFBQWE7UUFBQztJQUN0RSxHQUFHO1FBQUN4RTtLQUFxQjtJQUV6QjVELGdEQUFTQSxDQUFDO1FBQ04sSUFBSWdGLFlBQVk7WUFDWkMsY0FBYyxDQUFDb0Q7Z0JBQ1gsT0FBTztvQkFBRSxHQUFHQSxJQUFJO29CQUFFeEU7Z0JBQW9CO1lBQzFDO1FBQ0o7SUFDSixHQUFHO1FBQUNBO0tBQW9CO0lBRXhCLE1BQU1vRSxrQkFBa0IsT0FBT3ZFO1FBQzNCLElBQUlBLGtCQUFrQixHQUFHO1lBQ3JCLElBQUlLLFNBQVM7Z0JBQ1QsTUFBTXVFLFFBQ0YsTUFBTWxCLGtCQUFrQm1CLE9BQU8sQ0FBQzdFO2dCQUNwQyxJQUFJNEUsT0FBTzt3QkF1Q1FBLGdCQUNMQSxpQkFnQkZBLGlCQUdBQSw0QkFBQUEsaUJBSVNBLGlCQUVUQSxpQ0FBQUEsaUJBS0FBLGdDQUFBQSxpQkFFQUEsaUJBQ0NBLGlDQUFBQSxpQkFDQ0EsaUNBQUFBLGlCQUVFQSx3QkFFRUEsaUNBQUFBLGtCQUNDQSxpQ0FBQUE7b0JBOUVmckQsY0FBYzt3QkFDVnVELFlBQVlGLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBT0UsVUFBVSxJQUN2QkYsa0JBQUFBLDRCQUFBQSxNQUFPRSxVQUFVLEdBQ2pCO3dCQUNOQyxVQUFVSCxDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU9HLFFBQVEsSUFBR0gsa0JBQUFBLDRCQUFBQSxNQUFPRyxRQUFRLEdBQUc7d0JBQzlDQyxLQUFLSixDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU9JLEdBQUcsSUFBR0osa0JBQUFBLDRCQUFBQSxNQUFPSSxHQUFHLEdBQUc7d0JBQy9COUIsVUFBVTBCLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBTzFCLFFBQVEsSUFDbkIwQixrQkFBQUEsNEJBQUFBLE1BQU8xQixRQUFRLEdBQ2ZoRCxpQ0FBQUEsMkNBQUFBLHFCQUFzQmdELFFBQVE7d0JBQ3BDQyxXQUFXeUIsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPekIsU0FBUyxJQUNyQnlCLGtCQUFBQSw0QkFBQUEsTUFBT3pCLFNBQVMsR0FDaEJqRCxpQ0FBQUEsMkNBQUFBLHFCQUFzQmlELFNBQVM7d0JBQ3JDaEQscUJBQXFCeUUsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPekUsbUJBQW1CLElBQ3pDeUUsa0JBQUFBLDRCQUFBQSxNQUFPekUsbUJBQW1CLEdBQzFCO3dCQUNOOEUsY0FBY0wsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPSyxZQUFZLElBQzNCTCxrQkFBQUEsNEJBQUFBLE1BQU9LLFlBQVksR0FDbkI7d0JBQ05DLFlBQVlOLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBT00sVUFBVSxJQUN2Qk4sa0JBQUFBLDRCQUFBQSxNQUFPTSxVQUFVLEdBQ2pCO3dCQUNOQyxjQUFjUCxDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU9PLFlBQVksSUFDM0JQLGtCQUFBQSw0QkFBQUEsTUFBT08sWUFBWSxHQUNuQjt3QkFDTkMsT0FBT1IsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPUSxLQUFLLElBQUdSLGtCQUFBQSw0QkFBQUEsTUFBT1EsS0FBSyxHQUFHO3dCQUNyQ0MsV0FBV1QsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPUyxTQUFTLElBQUdULGtCQUFBQSw0QkFBQUEsTUFBT1MsU0FBUyxHQUFHO3dCQUNqREMsT0FBT1YsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPVSxLQUFLLElBQUdWLGtCQUFBQSw0QkFBQUEsTUFBT1UsS0FBSyxHQUFHO3dCQUNyQ0MsT0FBT1gsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPVyxLQUFLLElBQUdYLGtCQUFBQSw0QkFBQUEsTUFBT1csS0FBSyxHQUFHO3dCQUNyQ0MsU0FBU1osQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPWSxPQUFPLElBQUdaLGtCQUFBQSw0QkFBQUEsTUFBT1ksT0FBTyxHQUFHO3dCQUMzQ0MsY0FBY2IsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPYSxZQUFZLElBQzNCYixrQkFBQUEsNEJBQUFBLE1BQU9hLFlBQVksR0FDbkI7d0JBQ05DLGNBQWNkLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBT2MsWUFBWSxJQUMzQmQsa0JBQUFBLDRCQUFBQSxNQUFPYyxZQUFZLEdBQ25CO3dCQUNOQyxZQUFZZixDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU9nQixnQkFBZ0IsSUFDN0JoQixrQkFBQUEsNEJBQUFBLE1BQU9nQixnQkFBZ0IsR0FDdkIxRixpQ0FBQUEsMkNBQUFBLHFCQUFzQndFLGFBQWE7d0JBQ3pDbUIsV0FBV2pCLENBQUFBLGtCQUFBQSw2QkFBQUEsaUJBQUFBLE1BQU9rQixPQUFPLGNBQWRsQixxQ0FBQUEsZUFBZ0JtQixFQUFFLElBQ3ZCbkIsa0JBQUFBLDZCQUFBQSxrQkFBQUEsTUFBT2tCLE9BQU8sY0FBZGxCLHNDQUFBQSxnQkFBZ0JtQixFQUFFLEdBQ2xCO3dCQUNOQyxlQUFlcEIsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPb0IsYUFBYSxJQUM3QkEsY0FBY0MsTUFBTSxDQUFDLENBQUNDLFlBQ2xCdEIsa0JBQUFBLDRCQUFBQSxNQUFPb0IsYUFBYSxDQUNmRyxLQUFLLENBQUMsS0FDTkMsUUFBUSxDQUFDRixVQUFVekIsS0FBSyxLQUVqQyxFQUFFO3dCQUNSNEIsc0JBQXNCekIsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPeUIsb0JBQW9CLElBQzNDekIsa0JBQUFBLDRCQUFBQSxNQUFPeUIsb0JBQW9CLEdBQzNCO3dCQUNOQyx1QkFBdUIxQixDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU8wQixxQkFBcUIsSUFDN0MxQixrQkFBQUEsNEJBQUFBLE1BQU8wQixxQkFBcUIsR0FDNUI7b0JBQ1Y7b0JBQ0ExRixRQUFRZ0Usa0JBQUFBLDZCQUFBQSxrQkFBQUEsTUFBT2tCLE9BQU8sY0FBZGxCLHNDQUFBQSxnQkFBZ0IyQixXQUFXO29CQUNuQzlFLGVBQWU7d0JBQ1grRSxXQUFXLEVBQ1A1QixrQkFBQUEsNkJBQUFBLGtCQUFBQSxNQUFPa0IsT0FBTyxjQUFkbEIsdUNBQUFBLDZCQUFBQSxnQkFBZ0I0QixXQUFXLGNBQTNCNUIsaURBQUFBLDJCQUE2QjZCLFVBQVUsQ0FDbkMsS0FDQTt3QkFFUkMsV0FBVyxFQUFFOUIsa0JBQUFBLDZCQUFBQSxrQkFBQUEsTUFBT2tCLE9BQU8sY0FBZGxCLHNDQUFBQSxnQkFBZ0I4QixXQUFXO3dCQUN4Q0MsZ0JBQWdCLEVBQ1ovQixrQkFBQUEsNkJBQUFBLGtCQUFBQSxNQUFPa0IsT0FBTyxjQUFkbEIsdUNBQUFBLGtDQUFBQSxnQkFBZ0IrQixnQkFBZ0IsY0FBaEMvQixzREFBQUEsZ0NBQWtDNkIsVUFBVSxDQUN4QyxLQUNBO3dCQUVSRyxpQkFBaUIsRUFDYmhDLGtCQUFBQSw2QkFBQUEsa0JBQUFBLE1BQU9rQixPQUFPLGNBQWRsQix1Q0FBQUEsaUNBQUFBLGdCQUFnQjVCLGVBQWUsY0FBL0I0QixxREFBQUEsK0JBQWlDbUIsRUFBRTt3QkFDdkNNLG9CQUFvQixFQUNoQnpCLGtCQUFBQSw2QkFBQUEsa0JBQUFBLE1BQU9rQixPQUFPLGNBQWRsQixzQ0FBQUEsZ0JBQWdCeUIsb0JBQW9CO3dCQUN4Q1EsR0FBRyxFQUFFakMsa0JBQUFBLDZCQUFBQSxrQkFBQUEsTUFBT2tCLE9BQU8sY0FBZGxCLHVDQUFBQSxrQ0FBQUEsZ0JBQWdCNUIsZUFBZSxjQUEvQjRCLHNEQUFBQSxnQ0FBaUNpQyxHQUFHO3dCQUN6Q0MsSUFBSSxFQUFFbEMsa0JBQUFBLDZCQUFBQSxrQkFBQUEsTUFBT2tCLE9BQU8sY0FBZGxCLHVDQUFBQSxrQ0FBQUEsZ0JBQWdCNUIsZUFBZSxjQUEvQjRCLHNEQUFBQSxnQ0FBaUNrQyxJQUFJO29CQUMvQztvQkFDQWpGLFlBQVkrQyxrQkFBQUEsNkJBQUFBLHlCQUFBQSxNQUFPbUMsZUFBZSxjQUF0Qm5DLDZDQUFBQSx1QkFBd0JvQyxLQUFLO29CQUN6Qy9ELG1CQUFtQjt3QkFDZkMsUUFBUSxFQUFFMEIsa0JBQUFBLDZCQUFBQSxtQkFBQUEsTUFBT2tCLE9BQU8sY0FBZGxCLHdDQUFBQSxrQ0FBQUEsaUJBQWdCNUIsZUFBZSxjQUEvQjRCLHNEQUFBQSxnQ0FBaUNpQyxHQUFHO3dCQUM5QzFELFNBQVMsRUFBRXlCLGtCQUFBQSw2QkFBQUEsbUJBQUFBLE1BQU9rQixPQUFPLGNBQWRsQix3Q0FBQUEsa0NBQUFBLGlCQUFnQjVCLGVBQWUsY0FBL0I0QixzREFBQUEsZ0NBQWlDa0MsSUFBSTtvQkFDcEQ7b0JBQ0F6RCwwQkFBMEI7d0JBQ3RCSCxRQUFRLEVBQUUwQixrQkFBQUEsNEJBQUFBLE1BQU9pQyxHQUFHO3dCQUNwQjFELFNBQVMsRUFBRXlCLGtCQUFBQSw0QkFBQUEsTUFBT2tDLElBQUk7b0JBQzFCO29CQUNBMUcsdUJBQXVCd0Usa0JBQUFBLDRCQUFBQSxNQUFPekUsbUJBQW1CO2dCQUNyRDtZQUNKLE9BQU87Z0JBQ0g4RyxhQUFhO29CQUNUQyxXQUFXO3dCQUNQbkIsSUFBSSxDQUFDL0Y7b0JBQ1Q7Z0JBQ0o7WUFDSjtRQUNKO0lBQ0o7SUFFQSxNQUFNLENBQUNpSCxhQUFhLEdBQUd6Siw2REFBWUEsQ0FBQ0osNkVBQXlCQSxFQUFFO1FBQzNEK0osYUFBYTtRQUNiQyxhQUFhLENBQUNDO1lBQ1YsTUFBTXpDLFFBQVF5QyxTQUFTQyw2QkFBNkI7WUFDcEQsSUFBSTFDLE9BQU87b0JBbUNRQSxnQkFBcUJBLGlCQWU1QkEsaUJBRVNBLDRCQUFBQSxpQkFJQUEsaUJBRVRBLGlDQUFBQSxpQkFJZUEsZ0NBQUFBLGlCQUVmQSxpQkFDQ0EsaUNBQUFBLGlCQUNDQSxpQ0FBQUEsaUJBRUVBLHdCQUVFQSxpQ0FBQUEsa0JBQ0NBLGlDQUFBQTtnQkF0RWZyRCxjQUFjO29CQUNWdUQsWUFBWUYsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPRSxVQUFVLElBQUdGLGtCQUFBQSw0QkFBQUEsTUFBT0UsVUFBVSxHQUFHO29CQUNwREMsVUFBVUgsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPRyxRQUFRLElBQUdILGtCQUFBQSw0QkFBQUEsTUFBT0csUUFBUSxHQUFHO29CQUM5Q0MsS0FBS0osQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPSSxHQUFHLElBQUdKLGtCQUFBQSw0QkFBQUEsTUFBT0ksR0FBRyxHQUFHO29CQUMvQjlCLFVBQVUwQixDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU8xQixRQUFRLElBQ25CMEIsa0JBQUFBLDRCQUFBQSxNQUFPMUIsUUFBUSxHQUNmaEQsaUNBQUFBLDJDQUFBQSxxQkFBc0JnRCxRQUFRO29CQUNwQ0MsV0FBV3lCLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBT3pCLFNBQVMsSUFDckJ5QixrQkFBQUEsNEJBQUFBLE1BQU96QixTQUFTLEdBQ2hCakQsaUNBQUFBLDJDQUFBQSxxQkFBc0JpRCxTQUFTO29CQUNyQ2hELHFCQUFxQnlFLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBT3pFLG1CQUFtQixJQUN6Q3lFLGtCQUFBQSw0QkFBQUEsTUFBT3pFLG1CQUFtQixHQUMxQjtvQkFDTjhFLGNBQWNMLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBT0ssWUFBWSxJQUMzQkwsa0JBQUFBLDRCQUFBQSxNQUFPSyxZQUFZLEdBQ25CO29CQUNOQyxZQUFZTixDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU9NLFVBQVUsSUFBR04sa0JBQUFBLDRCQUFBQSxNQUFPTSxVQUFVLEdBQUc7b0JBQ3BEQyxjQUFjUCxDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU9PLFlBQVksSUFDM0JQLGtCQUFBQSw0QkFBQUEsTUFBT08sWUFBWSxHQUNuQjtvQkFDTkMsT0FBT1IsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPUSxLQUFLLElBQUdSLGtCQUFBQSw0QkFBQUEsTUFBT1EsS0FBSyxHQUFHO29CQUNyQ0MsV0FBV1QsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPUyxTQUFTLElBQUdULGtCQUFBQSw0QkFBQUEsTUFBT1MsU0FBUyxHQUFHO29CQUNqREMsT0FBT1YsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPVSxLQUFLLElBQUdWLGtCQUFBQSw0QkFBQUEsTUFBT1UsS0FBSyxHQUFHO29CQUNyQ0MsT0FBT1gsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPVyxLQUFLLElBQUdYLGtCQUFBQSw0QkFBQUEsTUFBT1csS0FBSyxHQUFHO29CQUNyQ0MsU0FBU1osQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPWSxPQUFPLElBQUdaLGtCQUFBQSw0QkFBQUEsTUFBT1ksT0FBTyxHQUFHO29CQUMzQ0MsY0FBY2IsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPYSxZQUFZLElBQzNCYixrQkFBQUEsNEJBQUFBLE1BQU9hLFlBQVksR0FDbkI7b0JBQ05DLGNBQWNkLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBT2MsWUFBWSxJQUMzQmQsa0JBQUFBLDRCQUFBQSxNQUFPYyxZQUFZLEdBQ25CO29CQUNOQyxZQUFZZixDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU9nQixnQkFBZ0IsSUFDN0JoQixrQkFBQUEsNEJBQUFBLE1BQU9nQixnQkFBZ0IsR0FDdkIxRixpQ0FBQUEsMkNBQUFBLHFCQUFzQndFLGFBQWE7b0JBQ3pDbUIsV0FBV2pCLENBQUFBLGtCQUFBQSw2QkFBQUEsaUJBQUFBLE1BQU9rQixPQUFPLGNBQWRsQixxQ0FBQUEsZUFBZ0JtQixFQUFFLElBQUduQixrQkFBQUEsNkJBQUFBLGtCQUFBQSxNQUFPa0IsT0FBTyxjQUFkbEIsc0NBQUFBLGdCQUFnQm1CLEVBQUUsR0FBRztvQkFDckRDLGVBQWVwQixDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU9vQixhQUFhLElBQzdCQSxjQUFjQyxNQUFNLENBQUMsQ0FBQ0MsWUFDbEJ0QixrQkFBQUEsNEJBQUFBLE1BQU9vQixhQUFhLENBQ2ZHLEtBQUssQ0FBQyxLQUNOQyxRQUFRLENBQUNGLFVBQVV6QixLQUFLLEtBRWpDLEVBQUU7b0JBQ1I0QixzQkFBc0J6QixDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU95QixvQkFBb0IsSUFDM0N6QixrQkFBQUEsNEJBQUFBLE1BQU95QixvQkFBb0IsR0FDM0I7b0JBQ05DLHVCQUF1QjFCLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBTzBCLHFCQUFxQixJQUM3QzFCLGtCQUFBQSw0QkFBQUEsTUFBTzBCLHFCQUFxQixHQUM1QjtnQkFDVjtnQkFDQTFGLFFBQVFnRSxrQkFBQUEsNkJBQUFBLGtCQUFBQSxNQUFPa0IsT0FBTyxjQUFkbEIsc0NBQUFBLGdCQUFnQjJCLFdBQVc7Z0JBQ25DOUUsZUFBZTtvQkFDWCtFLFdBQVcsRUFBRTVCLGtCQUFBQSw2QkFBQUEsa0JBQUFBLE1BQU9rQixPQUFPLGNBQWRsQix1Q0FBQUEsNkJBQUFBLGdCQUFnQjRCLFdBQVcsY0FBM0I1QixpREFBQUEsMkJBQTZCNkIsVUFBVSxDQUNoRCxLQUNBO29CQUVKQyxXQUFXLEVBQUU5QixrQkFBQUEsNkJBQUFBLGtCQUFBQSxNQUFPa0IsT0FBTyxjQUFkbEIsc0NBQUFBLGdCQUFnQjhCLFdBQVc7b0JBQ3hDQyxnQkFBZ0IsRUFDWi9CLGtCQUFBQSw2QkFBQUEsa0JBQUFBLE1BQU9rQixPQUFPLGNBQWRsQix1Q0FBQUEsa0NBQUFBLGdCQUFnQitCLGdCQUFnQixjQUFoQy9CLHNEQUFBQSxnQ0FBa0M2QixVQUFVLENBQ3hDLEtBQ0E7b0JBRVJHLGlCQUFpQixFQUFFaEMsa0JBQUFBLDZCQUFBQSxrQkFBQUEsTUFBT2tCLE9BQU8sY0FBZGxCLHVDQUFBQSxpQ0FBQUEsZ0JBQWdCNUIsZUFBZSxjQUEvQjRCLHFEQUFBQSwrQkFBaUNtQixFQUFFO29CQUN0RE0sb0JBQW9CLEVBQ2hCekIsa0JBQUFBLDZCQUFBQSxrQkFBQUEsTUFBT2tCLE9BQU8sY0FBZGxCLHNDQUFBQSxnQkFBZ0J5QixvQkFBb0I7b0JBQ3hDUSxHQUFHLEVBQUVqQyxrQkFBQUEsNkJBQUFBLGtCQUFBQSxNQUFPa0IsT0FBTyxjQUFkbEIsdUNBQUFBLGtDQUFBQSxnQkFBZ0I1QixlQUFlLGNBQS9CNEIsc0RBQUFBLGdDQUFpQ2lDLEdBQUc7b0JBQ3pDQyxJQUFJLEVBQUVsQyxrQkFBQUEsNkJBQUFBLGtCQUFBQSxNQUFPa0IsT0FBTyxjQUFkbEIsdUNBQUFBLGtDQUFBQSxnQkFBZ0I1QixlQUFlLGNBQS9CNEIsc0RBQUFBLGdDQUFpQ2tDLElBQUk7Z0JBQy9DO2dCQUNBakYsWUFBWStDLGtCQUFBQSw2QkFBQUEseUJBQUFBLE1BQU9tQyxlQUFlLGNBQXRCbkMsNkNBQUFBLHVCQUF3Qm9DLEtBQUs7Z0JBQ3pDL0QsbUJBQW1CO29CQUNmQyxRQUFRLEVBQUUwQixrQkFBQUEsNkJBQUFBLG1CQUFBQSxNQUFPa0IsT0FBTyxjQUFkbEIsd0NBQUFBLGtDQUFBQSxpQkFBZ0I1QixlQUFlLGNBQS9CNEIsc0RBQUFBLGdDQUFpQ2lDLEdBQUc7b0JBQzlDMUQsU0FBUyxFQUFFeUIsa0JBQUFBLDZCQUFBQSxtQkFBQUEsTUFBT2tCLE9BQU8sY0FBZGxCLHdDQUFBQSxrQ0FBQUEsaUJBQWdCNUIsZUFBZSxjQUEvQjRCLHNEQUFBQSxnQ0FBaUNrQyxJQUFJO2dCQUNwRDtnQkFDQXpELDBCQUEwQjtvQkFDdEJILFFBQVEsRUFBRTBCLGtCQUFBQSw0QkFBQUEsTUFBT2lDLEdBQUc7b0JBQ3BCMUQsU0FBUyxFQUFFeUIsa0JBQUFBLDRCQUFBQSxNQUFPa0MsSUFBSTtnQkFDMUI7Z0JBQ0ExRyx1QkFBdUJ3RSxrQkFBQUEsNEJBQUFBLE1BQU96RSxtQkFBbUI7WUFDckQ7UUFDSjtRQUNBb0gsU0FBUyxDQUFDQztZQUNOQyxRQUFRRCxLQUFLLENBQUMsK0JBQStCQTtRQUNqRDtJQUNKO0lBRUEsTUFBTUUsc0JBQXNCLENBQUN4RztZQUVyQkE7UUFESkMsV0FDSUQsb0JBQUFBLCtCQUFBQSxrQkFBQUEsUUFDTStFLE1BQU0sQ0FDSixDQUFDMEIsU0FDR0EsT0FBT0MsUUFBUSxJQUFJLFNBQVNELE9BQU9FLFNBQVMsSUFBSSxpQkFINUQzRyxzQ0FBQUEsZ0JBS000RyxHQUFHLENBQUMsQ0FBQ0gsU0FBaUI7Z0JBQ3BCSSxPQUFPSixPQUFPRSxTQUFTLEdBQUcsTUFBTUYsT0FBT0ssT0FBTztnQkFDOUN2RCxPQUFPa0QsT0FBTzVCLEVBQUU7WUFDcEI7SUFFWjtJQUVBMUksdUVBQXFCQSxDQUFDcUsscUJBQXFCckg7SUFFM0MsTUFBTTRILDBCQUEwQixDQUFDaEU7UUFDN0JoRCxlQUFlN0UsNENBQUtBLENBQUM2SCxNQUFNcEQsTUFBTSxDQUFDO0lBQ2xDLHVCQUF1QjtJQUMzQjtJQUVBdkUsZ0RBQVNBLENBQUM7UUFDTixJQUFJc0QsY0FBYztZQUNkYyxhQUNJZCxhQUFha0ksR0FBRyxDQUFDLENBQUNJLFdBQW1CO29CQUNqQ0gsT0FBT0csU0FBU0MsS0FBSztvQkFDckIxRCxPQUFPeUQsU0FBU25DLEVBQUU7b0JBQ2xCN0MsVUFBVWdGLFNBQVNyQixHQUFHO29CQUN0QjFELFdBQVcrRSxTQUFTcEIsSUFBSTtnQkFDNUI7UUFFUjtJQUNKLEdBQUc7UUFBQ2xIO0tBQWE7SUFFakIsTUFBTXdJLGNBQWM7UUFDaEI7WUFBRUwsT0FBTztZQUFjdEQsT0FBTztRQUFhO1FBQzNDO1lBQUVzRCxPQUFPO1lBQWN0RCxPQUFPO1FBQWE7UUFDM0Msc0NBQXNDO1FBQ3RDO1lBQUVzRCxPQUFPO1lBQVF0RCxPQUFPO1FBQU87UUFDL0I7WUFBRXNELE9BQU87WUFBaUJ0RCxPQUFPO1FBQWdCO1FBQ2pEO1lBQUVzRCxPQUFPO1lBQU90RCxPQUFPO1FBQU07UUFDN0I7WUFBRXNELE9BQU87WUFBU3RELE9BQU87UUFBUTtLQUNwQztJQUVELE1BQU00RCxXQUFXO1FBQ2I7WUFBRU4sT0FBTztZQUFhdEQsT0FBTztRQUFZO1FBQ3pDO1lBQUVzRCxPQUFPO1lBQWF0RCxPQUFPO1FBQVk7UUFDekM7WUFBRXNELE9BQU87WUFBV3RELE9BQU87UUFBVTtRQUNyQztZQUFFc0QsT0FBTztZQUFhdEQsT0FBTztRQUFZO1FBQ3pDO1lBQUVzRCxPQUFPO1lBQWF0RCxPQUFPO1FBQVk7S0FDNUM7SUFFRCxNQUFNNkQsb0JBQW9CO1FBQ3RCO1lBQUVQLE9BQU87WUFBc0J0RCxPQUFPO1FBQXFCO1FBQzNEO1lBQUVzRCxPQUFPO1lBQXFCdEQsT0FBTztRQUFvQjtRQUN6RDtZQUFFc0QsT0FBTztZQUFxQnRELE9BQU87UUFBb0I7UUFDekQ7WUFBRXNELE9BQU87WUFBaUJ0RCxPQUFPO1FBQWdCO1FBQ2pEO1lBQUVzRCxPQUFPO1lBQWV0RCxPQUFPO1FBQWM7UUFDN0M7WUFBRXNELE9BQU87WUFBbUJ0RCxPQUFPO1FBQWtCO1FBQ3JEO1lBQUVzRCxPQUFPO1lBQVl0RCxPQUFPO1FBQVc7UUFDdkM7WUFBRXNELE9BQU87WUFBY3RELE9BQU87UUFBYTtRQUMzQztZQUFFc0QsT0FBTztZQUFTdEQsT0FBTztRQUFRO0tBQ3BDO0lBRUQsTUFBTThELGVBQWU7UUFDakI7WUFBRVIsT0FBTztZQUFXdEQsT0FBTztRQUFVO1FBQ3JDO1lBQUVzRCxPQUFPO1lBQVl0RCxPQUFPO1FBQVc7UUFDdkM7WUFBRXNELE9BQU87WUFBWXRELE9BQU87UUFBVztLQUMxQztJQUVELE1BQU11QixnQkFBZ0I7UUFDbEI7WUFDSStCLE9BQU87WUFDUHRELE9BQU87UUFDWDtRQUNBO1lBQUVzRCxPQUFPO1lBQWlCdEQsT0FBTztRQUFnQjtRQUNqRDtZQUFFc0QsT0FBTztZQUFrQnRELE9BQU87UUFBaUI7UUFDbkQ7WUFBRXNELE9BQU87WUFBV3RELE9BQU87UUFBVTtRQUNyQztZQUFFc0QsT0FBTztZQUF3QnRELE9BQU87UUFBdUI7UUFDL0Q7WUFBRXNELE9BQU87WUFBa0J0RCxPQUFPO1FBQWlCO1FBQ25EO1lBQUVzRCxPQUFPO1lBQWtCdEQsT0FBTztRQUFpQjtRQUNuRDtZQUFFc0QsT0FBTztZQUFhdEQsT0FBTztRQUFZO1FBQ3pDO1lBQUVzRCxPQUFPO1lBQWtCdEQsT0FBTztRQUFpQjtRQUNuRDtZQUFFc0QsT0FBTztZQUFTdEQsT0FBTztRQUFRO0tBQ3BDO0lBRUQsTUFBTStELHFCQUFxQjtRQUN2QixJQUFJbEgsQ0FBQUEsdUJBQUFBLGlDQUFBQSxXQUFZdUUsU0FBUyxNQUFLNEMsV0FBVztZQUNyQ2xLLDBDQUFLQSxDQUFDaUosS0FBSyxDQUNQO1lBRUp6RyxzQkFBc0I7WUFDdEI7UUFDSjtRQUNBLE1BQU1tRyxZQUFZO1lBQ2R3QixPQUFPO2dCQUNIQyxhQUFhakgsQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFhaUgsV0FBVyxJQUMvQmpILHdCQUFBQSxrQ0FBQUEsWUFBYWlILFdBQVcsR0FDeEI7Z0JBQ05qQyxhQUFhdEYsVUFBVUEsVUFBVTtnQkFDakNULE1BQU1LLGNBQ0E1RSw0Q0FBS0EsR0FBR3lFLE1BQU0sQ0FBQyxnQkFBZ0IsTUFBTUcsY0FDckM1RSw0Q0FBS0EsR0FBR3lFLE1BQU0sQ0FBQztnQkFDckIrSCxRQUFRLEVBQUVsSCx3QkFBQUEsa0NBQUFBLFlBQWFrSCxRQUFRO2dCQUMvQixvQ0FBb0M7Z0JBQ3BDQyxnQkFBZ0I3STtZQUNwQjtRQUNKO1FBRUEsSUFBSTBCLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYXFFLEVBQUUsSUFBRyxHQUFHO1lBQ3JCLElBQUkxRixTQUFTO2dCQUNULE1BQU11RCxxQkFBcUJrRixJQUFJLENBQUM7b0JBQzVCL0MsRUFBRSxFQUFFckUsd0JBQUFBLGtDQUFBQSxZQUFhcUUsRUFBRTtvQkFDbkIsR0FBR21CLFVBQVV3QixLQUFLO2dCQUN0QjtnQkFDQSw0Q0FBNEM7Z0JBQzVDM0gsc0JBQXNCO2dCQUN0QmdCLHdCQUF3QjtnQkFDeEJ3QyxnQkFBZ0J2RTtZQUNwQixPQUFPO2dCQUNIK0ksc0JBQXNCO29CQUNsQjdCLFdBQVc7d0JBQ1B3QixPQUFPOzRCQUNIM0MsRUFBRSxFQUFFckUsd0JBQUFBLGtDQUFBQSxZQUFhcUUsRUFBRTs0QkFDbkIsR0FBR21CLFVBQVV3QixLQUFLO3dCQUN0QjtvQkFDSjtnQkFDSjtZQUNKO1FBQ0osT0FBTztZQUNILElBQUlySSxTQUFTO2dCQUNULE1BQU11RCxxQkFBcUJrRixJQUFJLENBQUM7b0JBQzVCL0MsSUFBSWhJLGlGQUFnQkE7b0JBQ3BCLEdBQUdtSixVQUFVd0IsS0FBSztnQkFDdEI7Z0JBQ0EsNENBQTRDO2dCQUM1QzNILHNCQUFzQjtnQkFDdEJnQix3QkFBd0I7Z0JBQ3hCLE1BQU13QyxnQkFBZ0J2RTtZQUMxQixPQUFPO2dCQUNIZ0osc0JBQXNCO29CQUNsQjlCLFdBQVc7d0JBQ1B3QixPQUFPOzRCQUNILEdBQUd4QixVQUFVd0IsS0FBSzt3QkFDdEI7b0JBQ0o7Z0JBQ0o7WUFDSjtRQUNKO1FBQ0EzSCxzQkFBc0I7SUFDMUI7SUFFQSxNQUFNLENBQUNpSSxzQkFBc0IsR0FBR3ZMLDREQUFXQSxDQUFDViw0RUFBcUJBLEVBQUU7UUFDL0RxSyxhQUFhLENBQUNDO1lBQ1YsNENBQTRDO1lBQzVDdEcsc0JBQXNCO1lBQ3RCZ0Isd0JBQXdCO1lBQ3hCd0MsZ0JBQWdCdkU7UUFDcEI7UUFDQXVILFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLG1DQUFtQ0E7UUFDckQ7SUFDSjtJQUVBLE1BQU0sQ0FBQ3VCLHNCQUFzQixHQUFHdEwsNERBQVdBLENBQUNULDRFQUFxQkEsRUFBRTtRQUMvRG9LLGFBQWEsQ0FBQ0M7WUFDViw0Q0FBNEM7WUFDNUN0RyxzQkFBc0I7WUFDdEJnQix3QkFBd0I7WUFDeEJ3QyxnQkFBZ0J2RTtRQUNwQjtRQUNBdUgsU0FBUyxDQUFDQztZQUNOQyxRQUFRRCxLQUFLLENBQUMsbUNBQW1DQTtRQUNyRDtJQUNKO0lBRUEsTUFBTXlCLHFCQUFxQixDQUFDQztRQUN4QjdILFdBQVc2SDtJQUNmO0lBRUEsTUFBTUMsYUFBYTtZQVFHN0gsc0JBQ0EwQiwyQkFJQTFCLHVCQUNBMEIsNEJBa0JLMUI7UUEvQnZCLE1BQU00RixZQUFZO1lBQ2R3QixPQUFPO2dCQUNINUQsWUFBWXhELFdBQVd3RCxVQUFVO2dCQUNqQ0MsVUFBVXpELFdBQVd5RCxRQUFRO2dCQUM3QkMsS0FBSyxDQUFDMUQsV0FBVzBELEdBQUc7Z0JBQ3BCOUIsVUFDSTVCLFdBQVc0QixRQUFRLEdBQUcsS0FDaEI1Qix1QkFBQUEsV0FBVzRCLFFBQVEsY0FBbkI1QiwyQ0FBQUEscUJBQXFCOEgsUUFBUSxNQUM3QnBHLDRCQUFBQSxnQkFBZ0JFLFFBQVEsY0FBeEJGLGdEQUFBQSwwQkFBMEJvRyxRQUFRO2dCQUU1Q2pHLFdBQ0k3QixXQUFXNkIsU0FBUyxHQUFHLEtBQ2pCN0Isd0JBQUFBLFdBQVc2QixTQUFTLGNBQXBCN0IsNENBQUFBLHNCQUFzQjhILFFBQVEsTUFDOUJwRyw2QkFBQUEsZ0JBQWdCRyxTQUFTLGNBQXpCSCxpREFBQUEsMkJBQTJCb0csUUFBUTtnQkFDN0NqSixxQkFBcUJtQixXQUFXbkIsbUJBQW1CO2dCQUNuRDhFLGNBQWMsQ0FBQzNELFdBQVcyRCxZQUFZO2dCQUN0Q0MsWUFBWTVELFdBQVc0RCxVQUFVO2dCQUNqQ0MsY0FBYzdELFdBQVc2RCxZQUFZO2dCQUNyQ0MsT0FBTzlELFdBQVc4RCxLQUFLO2dCQUN2QkMsV0FBVy9ELFdBQVcrRCxTQUFTO2dCQUMvQkMsT0FBT2hFLFdBQVdnRSxLQUFLO2dCQUN2QkMsT0FBT2pFLFdBQVdpRSxLQUFLO2dCQUN2QkMsU0FBU2xFLFdBQVdrRSxPQUFPO2dCQUMzQkMsY0FBY25FLFdBQVdtRSxZQUFZO2dCQUNyQzRELGtCQUFrQjtnQkFDbEIzRCxjQUFjcEUsV0FBV29FLFlBQVk7Z0JBQ3JDRyxXQUFXdkUsV0FBV3VFLFNBQVM7Z0JBQy9CRCxrQkFDSXRFLFdBQVdxRSxVQUFVLEdBQUcsSUFDbEJyRSxXQUFXcUUsVUFBVSxHQUNyQnpGLHFCQUFxQndFLGFBQWE7Z0JBQzVDc0IsYUFBYSxHQUFFMUUsNEJBQUFBLFdBQVcwRSxhQUFhLGNBQXhCMUUsZ0RBQUFBLDBCQUNUd0csR0FBRyxDQUFDLENBQUM3SCxPQUFjQSxLQUFLd0UsS0FBSyxFQUM5QjZFLElBQUksQ0FBQztnQkFDVmpELHNCQUFzQi9FLFdBQVcrRSxvQkFBb0I7Z0JBQ3JEQyx1QkFBdUJoRixXQUFXZ0YscUJBQXFCO1lBQzNEO1FBQ0o7UUFFQSxJQUFJdEcsa0JBQWtCLEdBQUc7WUFDckIsSUFBSUssU0FBUztnQkFDVCxNQUFNa0osT0FBTyxNQUFNN0Ysa0JBQWtCb0YsSUFBSSxDQUFDO29CQUN0Qy9DLElBQUksQ0FBQy9GO29CQUNMLEdBQUdrSCxVQUFVd0IsS0FBSztnQkFDdEI7Z0JBQ0EsSUFBSXBILFdBQVd1RSxTQUFTLEdBQUcsR0FBRzt3QkFZakJ6QyxrQ0FDQ0E7b0JBWlYsTUFBTU8sb0JBQW9CbUYsSUFBSSxDQUFDO3dCQUMzQi9DLElBQUl6RSxXQUFXdUUsU0FBUzt3QkFDeEJXLGFBQWFoRixZQUFZZ0YsV0FBVzt3QkFDcENFLGFBQWFsRixZQUFZa0YsV0FBVzt3QkFDcENMLHNCQUNJN0UsWUFBWTZFLG9CQUFvQjt3QkFDcENNLGtCQUFrQm5GLFlBQVltRixnQkFBZ0I7d0JBQzlDSixhQUFhNUY7d0JBQ2JpRyxtQkFBbUJ0RixXQUFXcUUsVUFBVTt3QkFDeEM2RCxTQUFTLEVBQUNELGlCQUFBQSwyQkFBQUEsS0FBTXhELEVBQUU7d0JBQ2xCMEQsV0FBVzt3QkFDWDVDLEdBQUcsR0FBRXpELG1DQUFBQSx1QkFBdUJGLFFBQVEsY0FBL0JFLHVEQUFBQSxpQ0FBaUNnRyxRQUFRO3dCQUM5Q3RDLElBQUksR0FBRTFELG9DQUFBQSx1QkFBdUJELFNBQVMsY0FBaENDLHdEQUFBQSxrQ0FBa0NnRyxRQUFRO29CQUNwRDtnQkFDSixPQUFPO3dCQWFLaEcsbUNBR0FBO3dCQUhBQSwyQ0FHQUE7b0JBZlIsTUFBTU8sb0JBQW9CbUYsSUFBSSxDQUFDO3dCQUMzQi9DLElBQUloSSxpRkFBZ0JBO3dCQUNwQnlJLGFBQWFoRixZQUFZZ0YsV0FBVzt3QkFDcENFLGFBQWFsRixZQUFZa0YsV0FBVzt3QkFDcENMLHNCQUNJN0UsWUFBWTZFLG9CQUFvQjt3QkFDcENNLGtCQUFrQm5GLFlBQVltRixnQkFBZ0I7d0JBQzlDSixhQUFhNUY7d0JBQ2JpRyxtQkFBbUJ0RixXQUFXc0YsaUJBQWlCO3dCQUMvQzRDLFNBQVMsRUFBQ0QsaUJBQUFBLDJCQUFBQSxLQUFNeEQsRUFBRTt3QkFDbEIwRCxXQUFXO3dCQUNYNUMsS0FDSXpELENBQUFBLDZDQUFBQSxvQ0FBQUEsdUJBQXVCRixRQUFRLGNBQS9CRSx3REFBQUEsa0NBQWlDZ0csUUFBUSxnQkFBekNoRyx1REFBQUEsNENBQ0E7d0JBQ0owRCxNQUNJMUQsQ0FBQUEsOENBQUFBLHFDQUFBQSx1QkFBdUJELFNBQVMsY0FBaENDLHlEQUFBQSxtQ0FBa0NnRyxRQUFRLGdCQUExQ2hHLHdEQUFBQSw2Q0FDQTtvQkFDUjtnQkFDSjtnQkFDQXJELGlCQUFpQixDQUFDQyxpQkFBaUI7WUFDdkMsT0FBTztnQkFDSDBKLDZCQUE2QjtvQkFDekJ4QyxXQUFXO3dCQUNQd0IsT0FBTzs0QkFDSDNDLElBQUksQ0FBQy9GOzRCQUNMLEdBQUdrSCxVQUFVd0IsS0FBSzt3QkFDdEI7b0JBQ0o7Z0JBQ0o7WUFDSjtRQUNKLE9BQU87WUFDSCxJQUFJckksU0FBUztvQkFRS2lCLHVCQUNBMEIsNEJBSUExQix3QkFDQTBCLDZCQWlCSzFCO2dCQTlCbkIsTUFBTWlJLE9BQU8sTUFBTTdGLGtCQUFrQm9GLElBQUksQ0FBQztvQkFDdEMvQyxJQUFJaEksaUZBQWdCQTtvQkFDcEIrRyxZQUFZeEQsV0FBV3dELFVBQVU7b0JBQ2pDQyxVQUFVekQsV0FBV3lELFFBQVE7b0JBQzdCQyxLQUFLLENBQUMxRCxXQUFXMEQsR0FBRztvQkFDcEI5QixVQUNJNUIsV0FBVzRCLFFBQVEsR0FBRyxLQUNoQjVCLHdCQUFBQSxXQUFXNEIsUUFBUSxjQUFuQjVCLDRDQUFBQSxzQkFBcUI4SCxRQUFRLE1BQzdCcEcsNkJBQUFBLGdCQUFnQkUsUUFBUSxjQUF4QkYsaURBQUFBLDJCQUEwQm9HLFFBQVE7b0JBRTVDakcsV0FDSTdCLFdBQVc2QixTQUFTLEdBQUcsS0FDakI3Qix5QkFBQUEsV0FBVzZCLFNBQVMsY0FBcEI3Qiw2Q0FBQUEsdUJBQXNCOEgsUUFBUSxNQUM5QnBHLDhCQUFBQSxnQkFBZ0JHLFNBQVMsY0FBekJILGtEQUFBQSw0QkFBMkJvRyxRQUFRO29CQUM3Q2pKLHFCQUFxQm1CLFdBQVduQixtQkFBbUI7b0JBQ25EOEUsY0FBYyxDQUFDM0QsV0FBVzJELFlBQVk7b0JBQ3RDQyxZQUFZNUQsV0FBVzRELFVBQVU7b0JBQ2pDQyxjQUFjN0QsV0FBVzZELFlBQVk7b0JBQ3JDQyxPQUFPOUQsV0FBVzhELEtBQUs7b0JBQ3ZCQyxXQUFXL0QsV0FBVytELFNBQVM7b0JBQy9CQyxPQUFPaEUsV0FBV2dFLEtBQUs7b0JBQ3ZCQyxPQUFPakUsV0FBV2lFLEtBQUs7b0JBQ3ZCQyxTQUFTbEUsV0FBV2tFLE9BQU87b0JBQzNCQyxjQUFjbkUsV0FBV21FLFlBQVk7b0JBQ3JDNEQsa0JBQWtCO29CQUNsQjNELGNBQWNwRSxXQUFXb0UsWUFBWTtvQkFDckNHLFdBQVd2RSxXQUFXdUUsU0FBUztvQkFDL0JELGtCQUFrQnRFLFdBQVdxRSxVQUFVLEdBQ2pDckUsV0FBV3FFLFVBQVUsR0FDckJ6RixxQkFBcUJ3RSxhQUFhO29CQUN4Q3NCLGFBQWEsR0FBRTFFLDZCQUFBQSxXQUFXMEUsYUFBYSxjQUF4QjFFLGlEQUFBQSwyQkFDVHdHLEdBQUcsQ0FBQyxDQUFDN0gsT0FBY0EsS0FBS3dFLEtBQUssRUFDOUI2RSxJQUFJLENBQUM7b0JBQ1ZqRCxzQkFBc0IvRSxXQUFXK0Usb0JBQW9CO29CQUNyREMsdUJBQXVCaEYsV0FBV2dGLHFCQUFxQjtnQkFDM0Q7Z0JBQ0EsTUFBTTNDLG9CQUFvQm1GLElBQUksQ0FBQztvQkFDM0IvQyxJQUFJaEksaUZBQWdCQTtvQkFDcEJ5SSxhQUFhaEYsWUFBWWdGLFdBQVc7b0JBQ3BDRSxhQUFhbEYsWUFBWWtGLFdBQVc7b0JBQ3BDTCxzQkFBc0I3RSxZQUFZNkUsb0JBQW9CO29CQUN0RE0sa0JBQWtCbkYsWUFBWW1GLGdCQUFnQjtvQkFDOUNKLGFBQWE1RjtvQkFDYmlHLG1CQUFtQnRGLFdBQVdxRSxVQUFVLEdBQ2xDckUsV0FBV3FFLFVBQVUsR0FDckJ6RixxQkFBcUJ3RSxhQUFhO29CQUN4QzhFLFNBQVMsRUFBQ0QsaUJBQUFBLDJCQUFBQSxLQUFNeEQsRUFBRTtvQkFDbEIwRCxXQUFXO2dCQUNmO2dCQUNBMUosaUJBQWlCLEVBQUN3SixpQkFBQUEsMkJBQUFBLEtBQU14RCxFQUFFLEdBQUU7Z0JBQzVCakc7WUFDSixPQUFPO29CQVNtQndCLHVCQUNBMEIsNEJBSUExQix3QkFDQTBCLDZCQWtCSzFCO2dCQWhDM0JxSSw2QkFBNkI7b0JBQ3pCekMsV0FBVzt3QkFDUHdCLE9BQU87NEJBQ0g1RCxZQUFZeEQsV0FBV3dELFVBQVU7NEJBQ2pDQyxVQUFVekQsV0FBV3lELFFBQVE7NEJBQzdCQyxLQUFLLENBQUMxRCxXQUFXMEQsR0FBRzs0QkFDcEI5QixVQUNJNUIsV0FBVzRCLFFBQVEsR0FBRyxLQUNoQjVCLHdCQUFBQSxXQUFXNEIsUUFBUSxjQUFuQjVCLDRDQUFBQSxzQkFBcUI4SCxRQUFRLE1BQzdCcEcsNkJBQUFBLGdCQUFnQkUsUUFBUSxjQUF4QkYsaURBQUFBLDJCQUEwQm9HLFFBQVE7NEJBRTVDakcsV0FDSTdCLFdBQVc2QixTQUFTLEdBQUcsS0FDakI3Qix5QkFBQUEsV0FBVzZCLFNBQVMsY0FBcEI3Qiw2Q0FBQUEsdUJBQXNCOEgsUUFBUSxNQUM5QnBHLDhCQUFBQSxnQkFBZ0JHLFNBQVMsY0FBekJILGtEQUFBQSw0QkFBMkJvRyxRQUFROzRCQUM3Q2pKLHFCQUNJbUIsV0FBV25CLG1CQUFtQjs0QkFDbEM4RSxjQUFjLENBQUMzRCxXQUFXMkQsWUFBWTs0QkFDdENDLFlBQVk1RCxXQUFXNEQsVUFBVTs0QkFDakNDLGNBQWM3RCxXQUFXNkQsWUFBWTs0QkFDckNDLE9BQU85RCxXQUFXOEQsS0FBSzs0QkFDdkJDLFdBQVcvRCxXQUFXK0QsU0FBUzs0QkFDL0JDLE9BQU9oRSxXQUFXZ0UsS0FBSzs0QkFDdkJDLE9BQU9qRSxXQUFXaUUsS0FBSzs0QkFDdkJDLFNBQVNsRSxXQUFXa0UsT0FBTzs0QkFDM0JDLGNBQWNuRSxXQUFXbUUsWUFBWTs0QkFDckM0RCxrQkFBa0I7NEJBQ2xCM0QsY0FBY3BFLFdBQVdvRSxZQUFZOzRCQUNyQ0csV0FBV3ZFLFdBQVd1RSxTQUFTOzRCQUMvQkQsa0JBQWtCdEUsV0FBV3FFLFVBQVUsR0FDakNyRSxXQUFXcUUsVUFBVSxHQUNyQnpGLHFCQUFxQndFLGFBQWE7NEJBQ3hDc0IsYUFBYSxHQUFFMUUsNkJBQUFBLFdBQVcwRSxhQUFhLGNBQXhCMUUsaURBQUFBLDJCQUNUd0csR0FBRyxDQUFDLENBQUM3SCxPQUFjQSxLQUFLd0UsS0FBSyxFQUM5QjZFLElBQUksQ0FBQzs0QkFDVmpELHNCQUNJL0UsV0FBVytFLG9CQUFvQjs0QkFDbkNDLHVCQUNJaEYsV0FBV2dGLHFCQUFxQjt3QkFDeEM7b0JBQ0o7Z0JBQ0o7WUFDSjtRQUNKO0lBQ0o7SUFFQSx1REFBdUQ7SUFDdkQ3SiwwREFBbUJBLENBQ2ZrRCxLQUNBLElBQU87WUFDSG1KLE1BQU1LO1FBQ1YsSUFDQTtRQUFDQTtLQUFXO0lBR2hCLE1BQU0sQ0FBQ1EsNkJBQTZCLEdBQUdsTSw0REFBV0EsQ0FDOUNkLG1GQUE0QkEsRUFDNUI7UUFDSXlLLGFBQWEsQ0FBQ0M7WUFDVixNQUFNa0MsT0FBT2xDLFNBQVNzQyw0QkFBNEI7WUFDbERDLHFCQUFxQjtnQkFDakIxQyxXQUFXO29CQUNQd0IsT0FBTzt3QkFDSGxDLGFBQWFoRixZQUFZZ0YsV0FBVzt3QkFDcENFLGFBQWFsRixZQUFZa0YsV0FBVzt3QkFDcENMLHNCQUNJN0UsWUFBWTZFLG9CQUFvQjt3QkFDcENNLGtCQUFrQm5GLFlBQVltRixnQkFBZ0I7d0JBQzlDSixhQUFhNUY7d0JBQ2JpRyxtQkFBbUJ0RixXQUFXcUUsVUFBVSxHQUNsQ3JFLFdBQVdxRSxVQUFVLEdBQ3JCekYscUJBQXFCd0UsYUFBYTt3QkFDeEM4RSxTQUFTLEVBQUNELGlCQUFBQSwyQkFBQUEsS0FBTXhELEVBQUU7d0JBQ2xCMEQsV0FBVztvQkFDZjtnQkFDSjtZQUNKO1lBQ0ExSixpQkFBaUIsRUFBQ3dKLGlCQUFBQSwyQkFBQUEsS0FBTXhELEVBQUUsR0FBRTtZQUM1QmpHO1FBQ0o7UUFDQXlILFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDbEQ7SUFDSjtJQUdKLE1BQU0sQ0FBQ2tDLDZCQUE2QixHQUFHak0sNERBQVdBLENBQzlDYixtRkFBNEJBLEVBQzVCO1FBQ0l3SyxhQUFhLENBQUNDO1lBQ1YsTUFBTWtDLE9BQU9sQyxTQUFTcUMsNEJBQTRCO1lBQ2xELElBQUlwSSxXQUFXdUUsU0FBUyxHQUFHLEdBQUc7b0JBZVR6QyxrQ0FDQ0E7Z0JBZmxCeUcscUJBQXFCO29CQUNqQjNDLFdBQVc7d0JBQ1B3QixPQUFPOzRCQUNIM0MsSUFBSXpFLFdBQVd1RSxTQUFTOzRCQUN4QlcsYUFBYWhGLFlBQVlnRixXQUFXOzRCQUNwQ0UsYUFBYWxGLFlBQVlrRixXQUFXOzRCQUNwQ0wsc0JBQ0k3RSxZQUFZNkUsb0JBQW9COzRCQUNwQ00sa0JBQ0luRixZQUFZbUYsZ0JBQWdCOzRCQUNoQ0osYUFBYTVGOzRCQUNiaUcsbUJBQW1CdEYsV0FBV3FFLFVBQVU7NEJBQ3hDNkQsU0FBUyxFQUFDRCxpQkFBQUEsMkJBQUFBLEtBQU14RCxFQUFFOzRCQUNsQjBELFdBQVc7NEJBQ1g1QyxHQUFHLEdBQUV6RCxtQ0FBQUEsdUJBQXVCRixRQUFRLGNBQS9CRSx1REFBQUEsaUNBQWlDZ0csUUFBUTs0QkFDOUN0QyxJQUFJLEdBQUUxRCxvQ0FBQUEsdUJBQXVCRCxTQUFTLGNBQWhDQyx3REFBQUEsa0NBQWtDZ0csUUFBUTt3QkFDcEQ7b0JBQ0o7Z0JBQ0o7WUFDSixPQUFPO2dCQUNIUSxxQkFBcUI7b0JBQ2pCMUMsV0FBVzt3QkFDUHdCLE9BQU87NEJBQ0hsQyxhQUFhaEYsWUFBWWdGLFdBQVc7NEJBQ3BDRSxhQUFhbEYsWUFBWWtGLFdBQVc7NEJBQ3BDTCxzQkFDSTdFLFlBQVk2RSxvQkFBb0I7NEJBQ3BDTSxrQkFDSW5GLFlBQVltRixnQkFBZ0I7NEJBQ2hDSixhQUFhNUY7NEJBQ2JpRyxtQkFDSXRGLFdBQVdzRixpQkFBaUI7NEJBQ2hDNEMsU0FBUyxFQUFDRCxpQkFBQUEsMkJBQUFBLEtBQU14RCxFQUFFOzRCQUNsQjBELFdBQVc7NEJBQ1g1QyxLQUFLekQsdUJBQXVCRixRQUFRLENBQUNrRyxRQUFROzRCQUM3Q3RDLE1BQU0xRCx1QkFBdUJELFNBQVMsQ0FBQ2lHLFFBQVE7d0JBQ25EO29CQUNKO2dCQUNKO1lBQ0o7WUFDQXJKLGlCQUFpQixDQUFDQyxpQkFBaUI7UUFDdkM7UUFDQXVILFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDbEQ7SUFDSjtJQUdKLE1BQU0sQ0FBQ29DLHFCQUFxQixHQUFHbk0sNERBQVdBLENBQUNaLDJFQUFvQkEsRUFBRTtRQUM3RHVLLGFBQWEsQ0FBQ0MsWUFBYztRQUM1QkUsU0FBUyxDQUFDQztZQUNOQyxRQUFRRCxLQUFLLENBQUMsbUNBQW1DQTtRQUNyRDtJQUNKO0lBRUEsTUFBTSxDQUFDcUMscUJBQXFCLEdBQUdwTSw0REFBV0EsQ0FBQ1gsMkVBQW9CQSxFQUFFO1FBQzdEc0ssYUFBYSxDQUFDQyxZQUFjO1FBQzVCRSxTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FBQyxtQ0FBbUNBO1FBQ3JEO0lBQ0o7SUFFQSxNQUFNaEQsdUJBQXVCLENBQUNDO1FBQzFCLDhDQUE4QztRQUM5QyxJQUFJLENBQUNBLE9BQU87UUFFWix1RUFBdUU7UUFDdkUsSUFBSUEsTUFBTUEsS0FBSyxFQUFFO1lBQ2IseUNBQXlDO1lBQ3pDbEQsY0FBYyxDQUFDb0Q7Z0JBQ1gsT0FBTztvQkFDSCxHQUFHQSxJQUFJO29CQUNQZ0IsWUFBWSxDQUFDbEIsTUFBTUEsS0FBSztvQkFDeEJ2QixVQUFVO29CQUNWQyxXQUFXO2dCQUNmO1lBQ0o7UUFDSixPQUFPLElBQ0hzQixNQUFNdkIsUUFBUSxLQUFLdUYsYUFDbkJoRSxNQUFNdEIsU0FBUyxLQUFLc0YsV0FDdEI7WUFDRSxrQ0FBa0M7WUFDbENsSCxjQUFjLENBQUNvRDtnQkFDWCxPQUFPO29CQUNILEdBQUdBLElBQUk7b0JBQ1BnQixZQUFZO29CQUNaekMsVUFBVXVCLE1BQU12QixRQUFRO29CQUN4QkMsV0FBV3NCLE1BQU10QixTQUFTO2dCQUM5QjtZQUNKO1FBQ0o7SUFDSjtJQUVBLE1BQU0yRyw4QkFBOEIsQ0FBQ3JGO1FBQ2pDLDhDQUE4QztRQUM5QyxJQUFJLENBQUNBLE9BQU87UUFFWix1RUFBdUU7UUFDdkUsSUFBSUEsTUFBTUEsS0FBSyxFQUFFO1lBQ2IseUNBQXlDO1lBQ3pDaEQsZUFBZTtnQkFDWCxHQUFHRCxXQUFXO2dCQUNkb0YsbUJBQW1CLENBQUNuQyxNQUFNQSxLQUFLO2dCQUMvQm9DLEtBQUs7Z0JBQ0xDLE1BQU07WUFDVjtRQUNKLE9BQU8sSUFDSHJDLE1BQU12QixRQUFRLEtBQUt1RixhQUNuQmhFLE1BQU10QixTQUFTLEtBQUtzRixXQUN0QjtZQUNFLGtDQUFrQztZQUNsQ2hILGVBQWU7Z0JBQ1gsR0FBR0QsV0FBVztnQkFDZG9GLG1CQUFtQjtnQkFDbkJDLEtBQUtwQyxNQUFNdkIsUUFBUTtnQkFDbkI0RCxNQUFNckMsTUFBTXRCLFNBQVM7WUFDekI7UUFDSjtJQUNKO0lBRUEsTUFBTTRHLHNCQUFzQjtRQUN4QixJQUFJbEssZUFBZTtZQUNma0Isc0JBQXNCO1lBQ3RCa0ksbUJBQW1CO1lBQ25CdEgsZUFBZTtRQUNuQixPQUFPO1lBQ0hwRCwwQ0FBS0EsQ0FBQ2lKLEtBQUssQ0FDUDtRQUVSO0lBQ0o7SUFFQSxNQUFNd0Msb0JBQW9CLENBQUNDO1FBQ3ZCbEosc0JBQXNCO1FBQ3RCWSxlQUFlc0k7UUFDZmhCLG1CQUFtQmdCLFFBQVF2RCxXQUFXO1FBQ3RDekYsZUFBZTdFLDRDQUFLQSxDQUFDNk4sUUFBUXRKLElBQUksRUFBRUUsTUFBTSxDQUFDO0lBQzlDO0lBRUEsTUFBTXFKLHNCQUFzQixDQUFDQztRQUN6QixNQUFNRixVQUFVckkscUJBQUFBLCtCQUFBQSxTQUFVd0ksSUFBSSxDQUFDLENBQUNDLElBQVdBLEVBQUV0RSxFQUFFLEtBQUtvRTtRQUNwRCxJQUFJRixTQUFTO1lBQ1RsSSx3QkFBd0I7WUFDeEJKLGVBQWVzSTtRQUNuQjtJQUNKO0lBRUEsTUFBTUssdUJBQXVCO1FBQ3pCLElBQUlqSyxTQUFTO1lBQ1QsTUFBTXVELHFCQUFxQmtGLElBQUksQ0FBQztnQkFDNUIvQyxFQUFFLEVBQUVyRSx3QkFBQUEsa0NBQUFBLFlBQWFxRSxFQUFFO2dCQUNuQjZCLFVBQVU7WUFDZDtZQUNBN0csc0JBQXNCO1lBQ3RCZ0Isd0JBQXdCO1lBQ3hCd0MsZ0JBQWdCdkU7WUFDaEIrQix3QkFBd0I7UUFDNUIsT0FBTztZQUNIZ0gsc0JBQXNCO2dCQUNsQjdCLFdBQVc7b0JBQ1B3QixPQUFPO3dCQUNIM0MsRUFBRSxFQUFFckUsd0JBQUFBLGtDQUFBQSxZQUFhcUUsRUFBRTt3QkFDbkI2QixVQUFVO29CQUNkO2dCQUNKO1lBQ0o7WUFDQTdGLHdCQUF3QjtRQUM1QjtJQUNKO0lBQ0EsTUFBTXdJLCtCQUErQjtRQUNqQyw2Q0FBNkM7UUFDN0MsTUFBTXJKLFVBQVUsTUFBTXVDLFlBQVkrRyxNQUFNO1FBQ3hDOUMsb0JBQW9CeEc7SUFDeEI7SUFDQTVFLGdEQUFTQSxDQUFDO1FBQ04sSUFBSStELFNBQVM7WUFDVGtLO1FBQ0o7SUFDSixHQUFHO1FBQUNsSztLQUFRO0lBRVovQywrREFBYUEsQ0FBQyxDQUFDNEcsVUFBVS9CLFdBQVc5QjtJQUVwQyxNQUFNLENBQUNvSywwQ0FBMEMsR0FBR2pOLDZEQUFZQSxDQUM1RE4sbUZBQStCQSxFQUMvQjtRQUNJaUssYUFBYTtRQUNiQyxhQUFhLENBQUNDO1lBQ1YsSUFBSWtDLE9BQ0FsQyxTQUFTcUQsb0NBQW9DLENBQUMxRCxLQUFLO1lBQ3ZEckUsZUFBZTRHO1FBQ25CO1FBQ0FoQyxTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FDVCx5Q0FDQUE7UUFFUjtJQUNKO0lBR0osTUFBTW1ELG1CQUFtQixPQUFPN0g7UUFDNUJDLFdBQVdEO1FBQ1gsTUFBTThILGVBQWVDLE1BQU1DLElBQUksQ0FDM0IsSUFBSUMsSUFDQWpJLFFBQVFrSSxvQkFBb0IsQ0FBQ2hFLEtBQUssQ0FBQ2MsR0FBRyxDQUNsQyxDQUFDbUQsTUFBYUEsSUFBSUMsU0FBUyxJQUdyQ3BELEdBQUcsQ0FBQyxDQUFDN0gsT0FBVTtnQkFDYmlMLFdBQVdqTDtnQkFDWGtMLEtBQUtySSxRQUFRa0ksb0JBQW9CLENBQUNoRSxLQUFLLENBQ2xDZixNQUFNLENBQUMsQ0FBQ2dGLE1BQWFBLElBQUlDLFNBQVMsS0FBS2pMLE1BQ3ZDNkgsR0FBRyxDQUFDLENBQUNtRCxNQUFhQSxJQUFJbEYsRUFBRTtZQUNqQztRQUNBNkUsYUFBYVEsT0FBTyxDQUFDLE9BQU9DO1lBQ3hCLElBQ0lBLFFBQVFILFNBQVMsS0FDakIsNENBQ0Y7Z0JBQ0UsSUFBSTdLLFNBQVM7b0JBQ1QsTUFBTWtKLE9BQU8sTUFBTXpGLFdBQVd3SCxRQUFRLENBQUNELFFBQVFGLEdBQUc7b0JBQ2xEeEksZUFBZTRHO2dCQUNuQixPQUFPO29CQUNILE1BQU1nQyxlQUE2QixDQUFDO29CQUNwQ0EsYUFBYXhGLEVBQUUsR0FBRzt3QkFBRXlGLElBQUlILFFBQVFGLEdBQUc7b0JBQUM7b0JBQ3BDViwwQ0FBMEM7d0JBQ3RDdkQsV0FBVzs0QkFDUGpCLFFBQVFzRjt3QkFDWjtvQkFDSjtnQkFDSjtZQUNKO1FBQ0o7SUFDSjtJQUVBLE1BQU1FLHNCQUFzQixPQUFPMUY7UUFDL0IsSUFBSTFGLFNBQVM7WUFDVCxNQUFNa0osT0FBTyxNQUFNeEYsYUFBYWMsT0FBTyxDQUFDa0I7WUFDeEMsSUFBSXdELE1BQU07Z0JBQ05vQixpQkFBaUJwQjtZQUNyQjtRQUNKLE9BQU87WUFDSG1DLGtCQUFrQjtnQkFDZHhFLFdBQVc7b0JBQ1B5RSxnQkFBZ0IsQ0FBQzVGO2dCQUNyQjtZQUNKO1FBQ0o7SUFDSjtJQUVBLE1BQU0sQ0FBQzJGLGtCQUFrQixHQUFHbE8sNkRBQVlBLENBQUNMLDJFQUF1QkEsRUFBRTtRQUM5RGdLLGFBQWE7UUFDYkMsYUFBYSxDQUFDQztZQUNWLE1BQU1rQyxPQUFPbEMsU0FBU3VFLG1CQUFtQjtZQUN6QyxJQUFJckMsTUFBTTtnQkFDTm9CLGlCQUFpQnBCO1lBQ3JCO1FBQ0o7UUFDQWhDLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7UUFDN0M7SUFDSjtJQUVBbEwsZ0RBQVNBLENBQUM7UUFDTm1QLG9CQUFvQixDQUFDckg7SUFDekIsR0FBRyxFQUFFO0lBRUwsTUFBTSxDQUFDeUgsaUJBQWlCLEdBQUdyTyw2REFBWUEsQ0FBQ1AsNkRBQVNBLEVBQUU7UUFDL0NrSyxhQUFhO1FBQ2JDLGFBQWEsQ0FBQ0M7WUFDVixNQUFNa0MsT0FBT2xDLFNBQVN5RSxrQkFBa0I7WUFDeEMsSUFBSXZDLE1BQU07Z0JBQ04sTUFBTS9HLGFBQWErRyxLQUFLdkMsS0FBSyxDQUN4QmYsTUFBTSxDQUFDLENBQUM4RjtvQkFDTCxPQUFPLENBQUNBLEtBQUtoRyxFQUFFLEtBQUssQ0FBQzNEO2dCQUN6QixHQUNDMEYsR0FBRyxDQUFDLENBQUNIO29CQUNGLDBEQUEwRDtvQkFDMUQsZ0JBQWdCO29CQUNoQixnQkFBZ0I7b0JBQ2hCLE9BQU87b0JBQ1AsT0FBTzt3QkFDSEksT0FBTyxHQUE2QkosT0FBMUJBLE9BQU9FLFNBQVMsSUFBSSxJQUFHLEtBQXdCLE9BQXJCRixPQUFPSyxPQUFPLElBQUksSUFBS2dFLElBQUk7d0JBQy9EdkgsT0FBT2tELE9BQU81QixFQUFFO3dCQUNoQiwwQkFBMEI7d0JBQzFCa0csU0FBUzs0QkFDTHBFLFdBQVdGLE9BQU9FLFNBQVM7NEJBQzNCRyxTQUFTTCxPQUFPSyxPQUFPOzRCQUN2QmtFLFFBQVF2RSxPQUFPd0UsWUFBWTt3QkFDL0I7b0JBQ0o7Z0JBQ0o7Z0JBRUoxSixjQUFjRDtnQkFFZCxNQUFNdEIsVUFBVXNCLFdBQVd5RCxNQUFNLENBQUMsQ0FBQzBCO29CQUMvQixJQUFJLENBQUNqRixhQUFhO3dCQUNkLE9BQU87b0JBQ1g7b0JBQ0EsT0FDSSxDQUFDbUksTUFBTXVCLE9BQU8sQ0FBQzFKLGdCQUNmLENBQUNBLFlBQVkySixJQUFJLENBQ2IsQ0FBQ2hCLFVBQ0dBLFdBQ0FBLFFBQVFpQixVQUFVLElBQ2xCakIsUUFBUWlCLFVBQVUsQ0FBQ3ZHLEVBQUUsS0FBSzRCLE9BQU9sRCxLQUFLLElBQ3RDNEcsUUFBUWtCLFFBQVEsS0FBSztnQkFHckM7Z0JBRUEsTUFBTUMsZ0JBQWdCdEwsUUFBUStFLE1BQU0sQ0FDaEMsQ0FBQzBCLFNBQ0csQ0FBQy9FLG1CQUNELENBQUNpSSxNQUFNdUIsT0FBTyxDQUFDeEosb0JBQ2YsQ0FBQ0EsZ0JBQWdCd0QsUUFBUSxDQUFDLENBQUN1QixPQUFPbEQsS0FBSztnQkFHL0NsQyxxQkFBcUJpSztZQUN6QjtRQUNKO1FBQ0FqRixTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FBQywwQkFBMEJBO1FBQzVDO0lBQ0o7SUFFQSxNQUFNaUYsa0JBQWtCO1FBQ3BCLElBQUlwTSxTQUFTO1lBQ1QsTUFBTWtKLE9BQU8sTUFBTTFGLG1CQUFtQjZJLGFBQWEsQ0FBQ3hJO1lBQ3BEakMsa0JBQWtCc0g7WUFDbEIsSUFBSUEsTUFBTTtnQkFDTixNQUFNckksVUFBVXFJLEtBQ1h0RCxNQUFNLENBQUMsQ0FBQzhGLE9BQWMsQ0FBQ0EsS0FBS2hHLEVBQUUsS0FBSyxDQUFDakQsUUFBUTZKLE1BQU0sQ0FBQzVHLEVBQUUsRUFDckQrQixHQUFHLENBQUMsQ0FBQ0g7b0JBQ0YsTUFBTWlGLG1CQUNGM04sK0VBQTZCQSxDQUN6Qjt3QkFBQzBJO3FCQUFPLEVBQ1I7d0JBQUN6RjtxQkFBTyxDQUNYLENBQUMsRUFBRTtvQkFDUixPQUFPO3dCQUNINkYsT0FBTyxHQUE2QkosT0FBMUJBLE9BQU9FLFNBQVMsSUFBSSxJQUFHLEtBQXdCLE9BQXJCRixPQUFPSyxPQUFPLElBQUksSUFBS2dFLElBQUk7d0JBQy9EdkgsT0FBT2tELE9BQU81QixFQUFFO3dCQUNoQndELE1BQU1xRDt3QkFDTlgsU0FBUzs0QkFDTHBFLFdBQVdGLE9BQU9FLFNBQVM7NEJBQzNCRyxTQUFTTCxPQUFPSyxPQUFPOzRCQUN2QmtFLFFBQVF2RSxPQUFPd0UsWUFBWTt3QkFDL0I7b0JBQ0o7Z0JBQ0o7Z0JBQ0o1SixxQkFBcUJyQjtZQUN6QjtRQUNKLE9BQU87WUFDSCxNQUFNMkssaUJBQWlCO2dCQUNuQjNFLFdBQVc7b0JBQ1BqQixRQUFRO3dCQUNKNEcsVUFBVTs0QkFBRTlHLElBQUk7Z0NBQUUrRyxJQUFJNUk7NEJBQVM7d0JBQUU7d0JBQ2pDNkksWUFBWTs0QkFBRUQsSUFBSTt3QkFBTTtvQkFDNUI7Z0JBQ0o7WUFDSjtRQUNKO0lBQ0o7SUFFQXhRLGdEQUFTQSxDQUFDO1FBQ04sSUFBSStILFdBQVc7WUFDWG9JO1lBQ0FuSSxhQUFhO1FBQ2pCO0lBQ0osR0FBRztRQUFDRDtLQUFVO0lBRWQscUJBQ0ksOERBQUMySTtRQUFJOUIsV0FBVyxHQUF1QyxPQUFwQzVLLFNBQVMsd0JBQXdCLElBQUc7O1lBQ2xETCxTQUFTLG1DQUNOOzBCQUNJLDRFQUFDK007b0JBQUk5QixXQUFVOztzQ0FDWCw4REFBQzhCOzRCQUFJOUIsV0FBVTs7OENBQ1gsOERBQUN2TSwwREFBRUE7OENBQUM7Ozs7Ozs4Q0FDSiw4REFBQ0MseURBQUNBOzhDQUFDOzs7Ozs7Ozs7Ozs7c0NBS1AsOERBQUNvTzs0QkFBSTlCLFdBQVU7OzhDQUNYLDhEQUFDOEI7b0NBQUk5QixXQUFVOzhDQUNYLDRFQUFDL00sOERBQVFBO3dDQUNMOE8sU0FBUzNFO3dDQUNUN0QsS0FBSyxFQUFFNkQsOEJBQUFBLHdDQUFBQSxrQkFBbUI4QixJQUFJLENBQzFCLENBQUM4QyxVQUNHQSxRQUFRekksS0FBSyxLQUNiakQsd0JBQUFBLGtDQUFBQSxZQUFhbUYsZ0JBQWdCO3dDQUVyQ3dHLFVBQVUsQ0FBQzFJOzRDQUNQaEQsZUFBZTtnREFDWCxHQUFHRCxXQUFXO2dEQUNkbUYsZ0JBQWdCLEVBQUVsQyxrQkFBQUEsNEJBQUFBLE1BQU9BLEtBQUs7NENBQ2xDO3dDQUNKO3dDQUNBMkksYUFBWTs7Ozs7Ozs7Ozs7Z0NBR25CNUwsQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFhbUYsZ0JBQWdCLEtBQUkseUJBQzlCLDhEQUFDcUc7b0NBQUk5QixXQUFVOzhDQUNYLDRFQUFDOU0sOERBQVFBO3dDQUNMMkgsSUFBSzt3Q0FDTHNILE1BQU07d0NBQ05uQyxXQUFXO3dDQUNYa0MsYUFBWTt3Q0FDWjNJLE9BQ0lqRCxDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWE2RSxvQkFBb0IsSUFDM0I3RSx3QkFBQUEsa0NBQUFBLFlBQWE2RSxvQkFBb0IsR0FDakM7d0NBRVY4RyxVQUFVOzRDQUNOMUwsZUFBZTtnREFDWCxHQUFHRCxXQUFXO2dEQUNkNkUsc0JBQXNCLFNBQ1RrSCxjQUFjLENBQ25CLGlDQUVOOUksS0FBSzs0Q0FDWDt3Q0FDSjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVE1Qiw4REFBQ3VJO2dCQUFJOUIsV0FBVTswQkFDWCw0RUFBQzhCO29CQUFJOUIsV0FBVTs4QkFDWCw0RUFBQzhCO3dCQUFJOUIsV0FBVTs7NEJBQ1Z0SixhQUNHQSxxQkFBQUEsK0JBQUFBLFNBQVVrRyxHQUFHLENBQUMsQ0FBQ21DLHdCQUNYLDhEQUFDeEssc0VBQVVBO29DQUVQK04sUUFBUXZEO29DQUNSd0QsUUFBUXpEO29DQUNSMEQsVUFBVXhEO21DQUhMLEdBQXdCRCxPQUFyQkEsUUFBUWxFLEVBQUUsRUFBQyxZQUE2QixPQUFuQmtFLFFBQVF0SixJQUFJLElBQUk7Ozs7OzBDQU16RCw4REFBQ3FNO2dDQUFJOUIsV0FBVTs7a0RBQ1gsOERBQUM5TCxtREFBTUE7d0NBQ0h1TyxTQUFTLElBQ0xwSyx5QkFBeUI7a0RBQzNCOzs7Ozs7a0RBSU4sOERBQUNqRSxvREFBT0E7OzBEQUNKLDhEQUFDRSwyREFBY0E7Z0RBQUNvTyxPQUFPOzBEQUNuQiw0RUFBQ3hPLG1EQUFNQTtvREFDSHVPLFNBQVM1RDtvREFDVDhELFVBQVUsQ0FBQ2hPOzhEQUFlOzs7Ozs7Ozs7OzswREFJbEMsOERBQUNOLDJEQUFjQTtnREFDWHVPLFFBQVFqTyxDQUFBQSwwQkFBQUEsb0NBQUFBLGNBQWVrRyxFQUFFLElBQUc7MERBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFTdER4RixpQ0FDRyw4REFBQ3lNO2dCQUFJOUIsV0FBVTs7a0NBQ1gsOERBQUM5TCxtREFBTUE7d0JBQ0gyTyxTQUFRO3dCQUNSQyxVQUFVMVAsc0ZBQVNBO3dCQUNuQnFQLFNBQVMsSUFBTTdOO2tDQUFjOzs7Ozs7a0NBR2pDLDhEQUFDVixtREFBTUE7d0JBQUN1TyxTQUFTeEU7a0NBQVk7Ozs7Ozs7Ozs7OzswQkFHckMsOERBQUM5Syw0RUFBY0E7Z0JBQ1g0UCxZQUFZbk47Z0JBQ1pvTixlQUFlbk47Z0JBQ2ZvTixNQUFLO2dCQUNMQyxjQUFjNUY7Z0JBQ2RMLE9BQ0l6RyxDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFxRSxFQUFFLElBQUcsSUFDWixtQkFDQTtnQkFFVnNJLFlBQ0kzTSxDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFxRSxFQUFFLElBQUcsSUFBSSxXQUFXOzBCQUVyQyw0RUFBQ2lIO29CQUFJOUIsV0FBVTs7c0NBQ1gsOERBQUNoTix3REFBS0E7NEJBQ0ZvUSxTQUFROzRCQUNSdkcsT0FBTTs0QkFDTm1ELFdBQVU7c0NBQ1YsNEVBQUMvTSw4REFBUUE7Z0NBQ0w0SCxJQUFHO2dDQUNIa0gsU0FBUzFFO2dDQUNUOUQsS0FBSyxFQUFFOEQseUJBQUFBLG1DQUFBQSxhQUFjNkIsSUFBSSxDQUNyQixDQUFDbks7d0NBRUd5QjsyQ0FEQXpCLEtBQUt3RSxLQUFLLEtBQ1YvQyx3QkFBQUEsbUNBQUFBLDJCQUFBQSxZQUFhaUgsV0FBVyxjQUF4QmpILCtDQUFBQSx5QkFBMEIrRSxVQUFVLENBQ2hDLEtBQ0E7O2dDQUdaMEcsVUFBVSxDQUFDMUksUUFDUDlDLGVBQWU7d0NBQ1gsR0FBR0QsV0FBVzt3Q0FDZGlILFdBQVcsRUFBRWxFLGtCQUFBQSw0QkFBQUEsTUFBT0EsS0FBSztvQ0FDN0I7Z0NBRUoySSxhQUFZOzs7Ozs7Ozs7OztzQ0FJcEIsOERBQUNKOzRCQUFJOUIsV0FBVTs7OENBQ1gsOERBQUNoTix3REFBS0E7b0NBQ0ZvUSxTQUFRO29DQUNScEQsV0FBVTs4Q0FBMEI7Ozs7Ozs4Q0FHeEMsOERBQUN4Tix3REFBU0E7b0NBQ05pRCxNQUFNSztvQ0FDTmdELGtCQUFrQixDQUFDQzt3Q0FDZmdFLHdCQUF3QmhFO29DQUM1QjtvQ0FDQXNLLFFBQU87b0NBQ1BDLFdBQVU7Ozs7Ozs7Ozs7OztzQ0FJbEIsOERBQUN4Qjs0QkFBSTlCLFdBQVU7OzhDQUNYLDhEQUFDaE4sd0RBQUtBO29DQUFDb1EsU0FBUTs4Q0FBVTs7Ozs7OzhDQUN6Qiw4REFBQy9RLCtDQUFNQTtvQ0FDSHdJLElBQUc7b0NBQ0hxSCxhQUFZO29DQUNabEMsV0FBVTtvQ0FDVjlKLFNBQVNBO29DQUNUNkgsb0JBQW9CQTs7Ozs7Ozs7Ozs7O3NDQUk1Qiw4REFBQytEOzRCQUFJOUIsV0FBVTs7OENBQ1gsOERBQUNoTix3REFBS0E7b0NBQ0ZvUSxTQUFRO29DQUNScEQsV0FBVTs4Q0FBMEI7Ozs7OztnQ0FHdkNoSyx5QkFDRyw4REFBQy9DLDhEQUFRQTtvQ0FDTDRILElBQUc7b0NBQ0hrSCxTQUFTM0s7b0NBQ1RtQyxLQUFLLEVBQUVuQyw4QkFBQUEsd0NBQUFBLGtCQUFtQjhILElBQUksQ0FDMUIsQ0FBQ3pDOzRDQUVHakc7K0NBREFpRyxPQUFPbEQsS0FBSyxLQUNaL0Msd0JBQUFBLG1DQUFBQSxzQkFBQUEsWUFBYStNLE1BQU0sY0FBbkIvTSwwQ0FBQUEsb0JBQXFCcUUsRUFBRTs7b0NBRS9Cb0gsVUFBVSxDQUFDMUksUUFDUDlDLGVBQWU7NENBQ1gsR0FBR0QsV0FBVzs0Q0FDZGtILFFBQVEsRUFBRW5FLGtCQUFBQSw0QkFBQUEsTUFBT0EsS0FBSzt3Q0FDMUI7b0NBRUoySSxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNaEMsOERBQUMvTyw0RUFBY0E7Z0JBQ1g0UCxZQUFZbk07Z0JBQ1pvTSxlQUFlbk07Z0JBQ2ZxTSxjQUFjOUQ7Z0JBQ2RuQyxPQUFNO2dCQUNONEYsU0FBUTtnQkFDUk0sWUFBVzswQkFBaUI7Ozs7OzswQkFLaEMsOERBQUM3UCx3REFBS0E7Z0JBQ0ZrUSxNQUFNcEw7Z0JBQ05xTCxjQUFjLENBQUNELE9BQVNuTCx5QkFBeUJtTDswQkFDakQsNEVBQUNqUSwrREFBWUE7b0JBQ1RtUSxNQUFLO29CQUNMMUQsV0FBVTs7c0NBQ1YsOERBQUN4TSw4REFBV0E7Ozs7O3NDQUNaLDhEQUFDc087NEJBQUk5QixXQUFVOzs4Q0FDWCw4REFBQzdMLHVEQUFVQTs4Q0FDUCw0RUFBQzJOO3dDQUFJOUIsV0FBVTs7MERBRVgsOERBQUNyTSxzREFBSUE7O2tFQUNELDhEQUFDRSw0REFBVUE7d0RBQUNtTSxXQUFVOzswRUFDbEIsOERBQUM4QjtnRUFBSTlCLFdBQVU7MEVBQ1gsNEVBQUM4QjtvRUFBSTlCLFdBQVU7OEVBQXVDOzs7Ozs7Ozs7OzswRUFJMUQsOERBQUN0TSx5REFBQ0E7MEVBQUM7Ozs7Ozs7Ozs7OztrRUFLUCw4REFBQ0UsNkRBQVdBO3dEQUFDb00sV0FBVTs7MEVBQ25CLDhEQUFDOEI7Z0VBQUk5QixXQUFVOztrRkFDWCw4REFBQ2hOLHdEQUFLQTt3RUFBQ29RLFNBQVE7a0ZBQWM7Ozs7OztrRkFHN0IsOERBQUNyUSx3REFBS0E7d0VBQ0Y4SCxJQUFHO3dFQUNIOUYsTUFBSzt3RUFDTG1OLGFBQVk7d0VBQ1ozSSxPQUNJbkQsQ0FBQUEsdUJBQUFBLGlDQUFBQSxXQUFZd0QsVUFBVSxLQUN0Qjt3RUFFSnFJLFVBQVUsQ0FBQzBCOzRFQUNQdE4sY0FBYztnRkFDVixHQUFHRCxVQUFVO2dGQUNid0QsWUFDSStKLEVBQUVDLE1BQU0sQ0FBQ3JLLEtBQUs7NEVBQ3RCO3dFQUNKOzs7Ozs7Ozs7Ozs7MEVBSVIsOERBQUN1STtnRUFBSTlCLFdBQVU7O2tGQUNYLDhEQUFDaE4sd0RBQUtBO3dFQUFDb1EsU0FBUTtrRkFBWTs7Ozs7O2tGQUczQiw4REFBQ3JRLHdEQUFLQTt3RUFDRjhILElBQUc7d0VBQ0g5RixNQUFLO3dFQUNMbU4sYUFBWTt3RUFDWjNJLE9BQ0luRCxDQUFBQSx1QkFBQUEsaUNBQUFBLFdBQVl5RCxRQUFRLEtBQ3BCO3dFQUVKb0ksVUFBVSxDQUFDMEI7NEVBQ1B0TixjQUFjO2dGQUNWLEdBQUdELFVBQVU7Z0ZBQ2J5RCxVQUNJOEosRUFBRUMsTUFBTSxDQUFDckssS0FBSzs0RUFDdEI7d0VBQ0o7Ozs7Ozs7Ozs7OzswRUFJUiw4REFBQ3VJO2dFQUFJOUIsV0FBVTs7a0ZBQ1gsOERBQUNoTix3REFBS0E7d0VBQUNvUSxTQUFRO2tGQUFNOzs7Ozs7a0ZBR3JCLDhEQUFDclEsd0RBQUtBO3dFQUNGOEgsSUFBRzt3RUFDSDlGLE1BQUs7d0VBQ0xtTixhQUFZO3dFQUNaMkIsS0FBSzt3RUFDTHRLLE9BQU9uRCxDQUFBQSx1QkFBQUEsaUNBQUFBLFdBQVkwRCxHQUFHLEtBQUk7d0VBQzFCbUksVUFBVSxDQUFDMEI7NEVBQ1B0TixjQUFjO2dGQUNWLEdBQUdELFVBQVU7Z0ZBQ2IwRCxLQUFLNkosRUFBRUMsTUFBTSxDQUFDckssS0FBSzs0RUFDdkI7d0VBQ0o7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFPaEIsOERBQUM1RixzREFBSUE7O2tFQUNELDhEQUFDRSw0REFBVUE7d0RBQUNtTSxXQUFVOzswRUFDbEIsOERBQUM4QjtnRUFBSTlCLFdBQVU7MEVBQ1gsNEVBQUM4QjtvRUFBSTlCLFdBQVU7OEVBQXVDOzs7Ozs7Ozs7OzswRUFJMUQsOERBQUN0TSx5REFBQ0E7MEVBQUM7Ozs7Ozs7Ozs7OztrRUFLUCw4REFBQ0UsNkRBQVdBO3dEQUFDb00sV0FBVTs7MEVBQ25CLDhEQUFDOEI7Z0VBQUk5QixXQUFVOztrRkFDWCw4REFBQ2hOLHdEQUFLQTt3RUFBQ29RLFNBQVE7a0ZBQWdCOzs7Ozs7a0ZBRy9CLDhEQUFDclEsd0RBQUtBO3dFQUNGOEgsSUFBRzt3RUFDSDlGLE1BQUs7d0VBQ0xtTixhQUFZO3dFQUNaMkIsS0FBSzt3RUFDTHRLLE9BQ0luRCxDQUFBQSx1QkFBQUEsaUNBQUFBLFdBQVkyRCxZQUFZLEtBQ3hCO3dFQUVKa0ksVUFBVSxDQUFDMEI7NEVBQ1B0TixjQUFjO2dGQUNWLEdBQUdELFVBQVU7Z0ZBQ2IyRCxjQUNJNEosRUFBRUMsTUFBTSxDQUFDckssS0FBSzs0RUFDdEI7d0VBQ0o7Ozs7Ozs7Ozs7OzswRUFJUiw4REFBQ3VJO2dFQUFJOUIsV0FBVTs7a0ZBQ1gsOERBQUNoTix3REFBS0E7d0VBQUNvUSxTQUFRO2tGQUFjOzs7Ozs7a0ZBRzdCLDhEQUFDblEsOERBQVFBO3dFQUNMOE8sU0FBUzdFO3dFQUNUM0QsS0FBSyxFQUFFMkQsd0JBQUFBLGtDQUFBQSxZQUFhZ0MsSUFBSSxDQUNwQixDQUFDbkssT0FDR0EsS0FBS3dFLEtBQUssS0FDVm5ELHVCQUFBQSxpQ0FBQUEsV0FBWTRELFVBQVU7d0VBRTlCaUksVUFBVSxDQUFDMUk7NEVBQ1BsRCxjQUFjO2dGQUNWLEdBQUdELFVBQVU7Z0ZBQ2I0RCxVQUFVLEVBQ05ULGtCQUFBQSw0QkFBQUEsTUFBT0EsS0FBSzs0RUFDcEI7d0VBQ0o7d0VBQ0EySSxhQUFZOzs7Ozs7Ozs7Ozs7NERBSW5COUwsQ0FBQUEsdUJBQUFBLGlDQUFBQSxXQUFZNEQsVUFBVSxLQUNuQix5QkFDQSw4REFBQzhIO2dFQUFJOUIsV0FBVTs7a0ZBQ1gsOERBQUNoTix3REFBS0E7d0VBQUNvUSxTQUFRO2tGQUEwQjs7Ozs7O2tGQUd6Qyw4REFBQ2xRLDhEQUFRQTt3RUFDTDJILElBQUc7d0VBQ0hzSCxNQUFNO3dFQUNORCxhQUFZO3dFQUNaM0ksT0FDSW5ELENBQUFBLHVCQUFBQSxpQ0FBQUEsV0FBWWdGLHFCQUFxQixLQUNqQzt3RUFFSjZHLFVBQVUsQ0FBQzBCOzRFQUNQdE4sY0FBYztnRkFDVixHQUFHRCxVQUFVO2dGQUNiZ0YsdUJBQ0l1SSxFQUFFQyxNQUFNLENBQ0hySyxLQUFLOzRFQUNsQjt3RUFDSjs7Ozs7Ozs7Ozs7OzBFQUtaLDhEQUFDdUk7Z0VBQUk5QixXQUFVOztrRkFDWCw4REFBQ2hOLHdEQUFLQTt3RUFBQ29RLFNBQVE7a0ZBQU87Ozs7OztrRkFHdEIsOERBQUNyUSx3REFBS0E7d0VBQ0Y4SCxJQUFHO3dFQUNIOUYsTUFBSzt3RUFDTG1OLGFBQVk7d0VBQ1ozSSxPQUNJbkQsQ0FBQUEsdUJBQUFBLGlDQUFBQSxXQUFZNkQsWUFBWSxLQUN4Qjt3RUFFSmdJLFVBQVUsQ0FBQzBCOzRFQUNQdE4sY0FBYztnRkFDVixHQUFHRCxVQUFVO2dGQUNiNkQsY0FDSTBKLEVBQUVDLE1BQU0sQ0FBQ3JLLEtBQUs7NEVBQ3RCO3dFQUNKOzs7Ozs7Ozs7Ozs7MEVBSVIsOERBQUN1STtnRUFBSTlCLFdBQVU7O2tGQUNYLDhEQUFDaE4sd0RBQUtBO3dFQUFDb1EsU0FBUTtrRkFBUTs7Ozs7O2tGQUd2Qiw4REFBQ3JRLHdEQUFLQTt3RUFDRjhILElBQUc7d0VBQ0g5RixNQUFLO3dFQUNMbU4sYUFBWTt3RUFDWjNJLE9BQ0luRCxDQUFBQSx1QkFBQUEsaUNBQUFBLFdBQVk4RCxLQUFLLEtBQUk7d0VBRXpCK0gsVUFBVSxDQUFDMEI7NEVBQ1B0TixjQUFjO2dGQUNWLEdBQUdELFVBQVU7Z0ZBQ2I4RCxPQUFPeUosRUFBRUMsTUFBTSxDQUNWckssS0FBSzs0RUFDZDt3RUFDSjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQU9oQiw4REFBQzVGLHNEQUFJQTs7a0VBQ0QsOERBQUNFLDREQUFVQTt3REFBQ21NLFdBQVU7OzBFQUNsQiw4REFBQzhCO2dFQUFJOUIsV0FBVTswRUFDWCw0RUFBQzhCO29FQUFJOUIsV0FBVTs4RUFBdUM7Ozs7Ozs7Ozs7OzBFQUkxRCw4REFBQ3RNLHlEQUFDQTswRUFBQzs7Ozs7Ozs7Ozs7O2tFQU1QLDhEQUFDRSw2REFBV0E7d0RBQUNvTSxXQUFVOzswRUFDbkIsOERBQUM4QjtnRUFBSTlCLFdBQVU7O2tGQUNYLDhEQUFDOEI7d0VBQUk5QixXQUFVOzswRkFDWCw4REFBQ2hOLHdEQUFLQTtnRkFBQ29RLFNBQVE7MEZBQWE7Ozs7OzswRkFHNUIsOERBQUNyUSx3REFBS0E7Z0ZBQ0Y4SCxJQUFHO2dGQUNIOUYsTUFBSztnRkFDTG1OLGFBQVk7Z0ZBQ1ozSSxPQUNJbkQsQ0FBQUEsdUJBQUFBLGlDQUFBQSxXQUFZK0QsU0FBUyxLQUNyQjtnRkFFSjhILFVBQVUsQ0FBQzBCO29GQUNQdE4sY0FBYzt3RkFDVixHQUFHRCxVQUFVO3dGQUNiK0QsV0FDSXdKLEVBQUVDLE1BQU0sQ0FDSHJLLEtBQUs7b0ZBQ2xCO2dGQUNKOzs7Ozs7Ozs7Ozs7a0ZBSVIsOERBQUN1STt3RUFBSTlCLFdBQVU7OzBGQUNYLDhEQUFDaE4sd0RBQUtBO2dGQUFDb1EsU0FBUTswRkFBYzs7Ozs7OzBGQUc3Qiw4REFBQ3JRLHdEQUFLQTtnRkFDRjhILElBQUc7Z0ZBQ0g5RixNQUFLO2dGQUNMbU4sYUFBWTtnRkFDWjNJLE9BQ0luRCxDQUFBQSx1QkFBQUEsaUNBQUFBLFdBQVlnRSxLQUFLLEtBQ2pCO2dGQUVKNkgsVUFBVSxDQUFDMEI7b0ZBQ1B0TixjQUFjO3dGQUNWLEdBQUdELFVBQVU7d0ZBQ2JnRSxPQUFPdUosRUFBRUMsTUFBTSxDQUNWckssS0FBSztvRkFDZDtnRkFDSjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQUtaLDhEQUFDdUk7Z0VBQUk5QixXQUFVOztrRkFDWCw4REFBQzhCO3dFQUFJOUIsV0FBVTs7MEZBQ1gsOERBQUNoTix3REFBS0E7Z0ZBQUNvUSxTQUFROzBGQUFPOzs7Ozs7MEZBR3RCLDhEQUFDclEsd0RBQUtBO2dGQUNGOEgsSUFBRztnRkFDSDlGLE1BQUs7Z0ZBQ0xtTixhQUFZO2dGQUNaM0ksT0FDSW5ELENBQUFBLHVCQUFBQSxpQ0FBQUEsV0FBWW9FLFlBQVksS0FDeEI7Z0ZBRUp5SCxVQUFVLENBQUMwQjtvRkFDUHROLGNBQWM7d0ZBQ1YsR0FBR0QsVUFBVTt3RkFDYm9FLGNBQ0ltSixFQUFFQyxNQUFNLENBQ0hySyxLQUFLO29GQUNsQjtnRkFDSjs7Ozs7Ozs7Ozs7O2tGQUlSLDhEQUFDdUk7d0VBQUk5QixXQUFVOzswRkFDWCw4REFBQ2hOLHdEQUFLQTtnRkFBQ29RLFNBQVE7MEZBQWM7Ozs7OzswRkFHN0IsOERBQUNyUSx3REFBS0E7Z0ZBQ0Y4SCxJQUFHO2dGQUNIOUYsTUFBSztnRkFDTG1OLGFBQVk7Z0ZBQ1ozSSxPQUNJbkQsQ0FBQUEsdUJBQUFBLGlDQUFBQSxXQUFZaUUsS0FBSyxLQUNqQjtnRkFFSjRILFVBQVUsQ0FBQzBCO29GQUNQdE4sY0FBYzt3RkFDVixHQUFHRCxVQUFVO3dGQUNiaUUsT0FBT3NKLEVBQUVDLE1BQU0sQ0FDVnJLLEtBQUs7b0ZBQ2Q7Z0ZBQ0o7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRUFLWiw4REFBQ3VJO2dFQUFJOUIsV0FBVTs7a0ZBQ1gsOERBQUNoTix3REFBS0E7d0VBQUNvUSxTQUFRO2tGQUFnQjs7Ozs7O2tGQUcvQiw4REFBQ2xRLDhEQUFRQTt3RUFDTDJILElBQUc7d0VBQ0hzSCxNQUFNO3dFQUNORCxhQUFZO3dFQUNaM0ksT0FDSW5ELENBQUFBLHVCQUFBQSxpQ0FBQUEsV0FBWWtFLE9BQU8sS0FDbkI7d0VBRUoySCxVQUFVLENBQUMwQjs0RUFDUHROLGNBQWM7Z0ZBQ1YsR0FBR0QsVUFBVTtnRkFDYmtFLFNBQ0lxSixFQUFFQyxNQUFNLENBQUNySyxLQUFLOzRFQUN0Qjt3RUFDSjs7Ozs7Ozs7Ozs7OzBFQUlSLDhEQUFDdUk7Z0VBQUk5QixXQUFVOzBFQUNYLDRFQUFDaE4sd0RBQUtBO29FQUNGb1EsU0FBUTtvRUFDUnBELFdBQVU7b0VBQ1ZuRCxPQUFNO29FQUNOaUgsMkJBQ0ksOERBQUNoUSw4REFBUUE7d0VBQ0wrRyxJQUFHO3dFQUNIa0osU0FDSTNOLENBQUFBLHVCQUFBQSxpQ0FBQUEsV0FBWW1FLFlBQVksS0FDeEI7d0VBRUowSSxNQUFLO3dFQUNMZSxZQUFZO3dFQUNaQyxpQkFBaUIsQ0FDYkY7NEVBRUExTixjQUFjO2dGQUNWLEdBQUdELFVBQVU7Z0ZBQ2JtRSxjQUNJd0osWUFDQTs0RUFDUjt3RUFDSjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQVVoQyw4REFBQ2pDO29DQUFJOUIsV0FBVTs4Q0FFWCw0RUFBQzlMLG1EQUFNQTt3Q0FDSHVPLFNBQVMsSUFDTHBLLHlCQUF5QjtrREFDM0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFrQmxDOztRQWhvRHlCN0csNERBQWVBO1FBOEJyQnNCLDJEQUFhQTtRQTBJTFIseURBQVlBO1FBaVBIQyx3REFBV0E7UUFZWEEsd0RBQVdBO1FBeU5KQSx3REFBV0E7UUErQlhBLHdEQUFXQTtRQXNEbkJBLHdEQUFXQTtRQU9YQSx3REFBV0E7UUErSFVELHlEQUFZQTtRQW9FcENBLHlEQUFZQTtRQWlCYkEseURBQVlBOzs7O1FBOTZCbEJkLDREQUFlQTtRQThCckJzQiwyREFBYUE7UUEwSUxSLHlEQUFZQTtRQWlQSEMsd0RBQVdBO1FBWVhBLHdEQUFXQTtRQXlOSkEsd0RBQVdBO1FBK0JYQSx3REFBV0E7UUFzRG5CQSx3REFBV0E7UUFPWEEsd0RBQVdBO1FBK0hVRCx5REFBWUE7UUFvRXBDQSx5REFBWUE7UUFpQmJBLHlEQUFZQTs7OztBQXF0Qi9Da0MsbUJBQW1CMFAsV0FBVyxHQUFHO0FBRWpDLCtEQUFlMVAsa0JBQWtCQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvdWkvbG9nYm9vay9mb3Jtcy92ZXNzZWwtcmVzY3VlLWZpZWxkcy50c3g/MTUxNSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuXHJcbmltcG9ydCBkYXlqcyBmcm9tICdkYXlqcydcclxuaW1wb3J0IFJlYWN0LCB7XHJcbiAgICB1c2VFZmZlY3QsXHJcbiAgICB1c2VTdGF0ZSxcclxuICAgIGZvcndhcmRSZWYsXHJcbiAgICB1c2VJbXBlcmF0aXZlSGFuZGxlLFxyXG59IGZyb20gJ3JlYWN0J1xyXG5pbXBvcnQgeyB1c2VTZWFyY2hQYXJhbXMgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXHJcbmltcG9ydCB7XHJcbiAgICBDcmVhdGVFdmVudFR5cGVfVmVzc2VsUmVzY3VlLFxyXG4gICAgVXBkYXRlRXZlbnRUeXBlX1Zlc3NlbFJlc2N1ZSxcclxuICAgIENyZWF0ZUNHRXZlbnRNaXNzaW9uLFxyXG4gICAgVXBkYXRlQ0dFdmVudE1pc3Npb24sXHJcbiAgICBDcmVhdGVNaXNzaW9uVGltZWxpbmUsXHJcbiAgICBVcGRhdGVNaXNzaW9uVGltZWxpbmUsXHJcbn0gZnJvbSAnQC9hcHAvbGliL2dyYXBoUUwvbXV0YXRpb24nXHJcbmltcG9ydCB7XHJcbiAgICBDUkVXX0xJU1QsXHJcbiAgICBDcmV3TWVtYmVyc19Mb2dCb29rRW50cnlTZWN0aW9uLFxyXG4gICAgR0VUX0xPR0JPT0tfRU5UUllfQllfSUQsXHJcbiAgICBHZXRUcmlwRXZlbnRfVmVzc2VsUmVzY3VlLFxyXG59IGZyb20gJ0AvYXBwL2xpYi9ncmFwaFFML3F1ZXJ5J1xyXG5pbXBvcnQge1xyXG4gICAgR2V0TG9nQm9va0VudHJpZXNNZW1iZXJzLFxyXG4gICAgZ2V0U2VhTG9nc01lbWJlcnNMaXN0LFxyXG4gICAgZ2V0VmVzc2VsQnlJRCxcclxufSBmcm9tICdAL2FwcC9saWIvYWN0aW9ucydcclxuaW1wb3J0IEVkaXRvciBmcm9tICcuLi8uLi9lZGl0b3InXHJcbmltcG9ydCB7IHVzZUxhenlRdWVyeSwgdXNlTXV0YXRpb24gfSBmcm9tICdAYXBvbGxvL2NsaWVudCdcclxuaW1wb3J0IFRpbWVGaWVsZCBmcm9tICcuLi9jb21wb25lbnRzL3RpbWUnXHJcblxyXG5pbXBvcnQgU2VhTG9nc01lbWJlck1vZGVsIGZyb20gJ0AvYXBwL29mZmxpbmUvbW9kZWxzL3NlYUxvZ3NNZW1iZXInXHJcbmltcG9ydCBFdmVudFR5cGVfVmVzc2VsUmVzY3VlTW9kZWwgZnJvbSAnQC9hcHAvb2ZmbGluZS9tb2RlbHMvZXZlbnRUeXBlX1Zlc3NlbFJlc2N1ZSdcclxuaW1wb3J0IENHRXZlbnRNaXNzaW9uTW9kZWwgZnJvbSAnQC9hcHAvb2ZmbGluZS9tb2RlbHMvY2dFdmVudE1pc3Npb24nXHJcbmltcG9ydCBNaXNzaW9uVGltZWxpbmVNb2RlbCBmcm9tICdAL2FwcC9vZmZsaW5lL21vZGVscy9taXNzaW9uVGltZWxpbmUnXHJcbmltcG9ydCB7IGdlbmVyYXRlVW5pcXVlSWQgfSBmcm9tICdAL2FwcC9vZmZsaW5lL2hlbHBlcnMvZnVuY3Rpb25zJ1xyXG5pbXBvcnQgeyB1c2VNZWRpYVF1ZXJ5IH0gZnJvbSAnQHJlYWN0dXNlcy9jb3JlJ1xyXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9pbnB1dCdcclxuaW1wb3J0IHsgTGFiZWwgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvbGFiZWwnXHJcbmltcG9ydCB7IENvbWJvYm94IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NvbWJvQm94J1xyXG5pbXBvcnQgeyBUZXh0YXJlYSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS90ZXh0YXJlYSdcclxuaW1wb3J0IHsgU2VwYXJhdG9yIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3NlcGFyYXRvcidcclxuaW1wb3J0IHsgQWxlcnREaWFsb2dOZXcgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYWxlcnQtZGlhbG9nLW5ldydcclxuaW1wb3J0IHsgQXJyb3dMZWZ0IH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xyXG5pbXBvcnQgeyB0b2FzdCB9IGZyb20gJ3Nvbm5lcidcclxuaW1wb3J0IHtcclxuICAgIFNoZWV0LFxyXG4gICAgU2hlZXRDb250ZW50LFxyXG4gICAgU2hlZXRIZWFkZXIsXHJcbiAgICBTaGVldFRpdGxlLFxyXG59IGZyb20gJ0AvY29tcG9uZW50cy91aS9zaGVldCdcclxuaW1wb3J0IHsgSDMsIFAgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdHlwb2dyYXBoeSdcclxuaW1wb3J0IHtcclxuICAgIENhcmQsXHJcbiAgICBDYXJkQ29udGVudCxcclxuICAgIENhcmRIZWFkZXIsXHJcbiAgICBDYXJkVGl0bGUsXHJcbiAgICBDYXJkRGVzY3JpcHRpb24sXHJcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnXHJcbmltcG9ydCB7IENoZWNrYm94IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NoZWNrYm94J1xyXG5pbXBvcnQgeyBDcmV3TWVtYmVyIH0gZnJvbSAnLi4vLi4vY3Jldy90eXBlcydcclxuaW1wb3J0IHsgR2V0Q3Jld0xpc3RXaXRoVHJhaW5pbmdTdGF0dXMgfSBmcm9tICdAL2FwcC9saWIvYWN0aW9ucydcclxuaW1wb3J0IENyZXdNZW1iZXJzX0xvZ0Jvb2tFbnRyeVNlY3Rpb25Nb2RlbCBmcm9tICdAL2FwcC9vZmZsaW5lL21vZGVscy9jcmV3TWVtYmVyc19Mb2dCb29rRW50cnlTZWN0aW9uJ1xyXG5pbXBvcnQgTG9nQm9va0VudHJ5TW9kZWwgZnJvbSAnQC9hcHAvb2ZmbGluZS9tb2RlbHMvbG9nQm9va0VudHJ5J1xyXG5pbXBvcnQge1xyXG4gICAgQnV0dG9uLFxyXG4gICAgU2Nyb2xsQXJlYSxcclxuICAgIFRvb2x0aXAsXHJcbiAgICBUb29sdGlwQ29udGVudCxcclxuICAgIFRvb2x0aXBUcmlnZ2VyLFxyXG59IGZyb20gJ0AvY29tcG9uZW50cy91aSdcclxuaW1wb3J0IHsgUmVjb3JkQ2FyZCB9IGZyb20gJ0AvYXBwL3VpL21haW50ZW5hbmNlL3Rhc2svdGFzaydcclxuXHJcbmNvbnN0IFZlc3NlbFJlc2N1ZUZpZWxkcyA9IGZvcndhcmRSZWY8XHJcbiAgICB7IHNhdmU6ICgpID0+IFByb21pc2U8dm9pZD4gfSxcclxuICAgIHtcclxuICAgICAgICBnZW9Mb2NhdGlvbnM6IGFueVxyXG4gICAgICAgIHNlbGVjdGVkRXZlbnQ6IGFueVxyXG4gICAgICAgIGNsb3NlTW9kYWw6IGFueVxyXG4gICAgICAgIGhhbmRsZVNhdmVQYXJlbnQ6IGFueVxyXG4gICAgICAgIGN1cnJlbnRSZXNjdWVJRDogYW55XHJcbiAgICAgICAgdHlwZTogYW55XHJcbiAgICAgICAgZXZlbnRDdXJyZW50TG9jYXRpb246IGFueVxyXG4gICAgICAgIGxvY2F0aW9uRGVzY3JpcHRpb246IGFueVxyXG4gICAgICAgIHNldExvY2F0aW9uRGVzY3JpcHRpb246IGFueVxyXG4gICAgICAgIG9mZmxpbmU/OiBib29sZWFuXHJcbiAgICAgICAgbG9ja2VkPzogYm9vbGVhblxyXG4gICAgICAgIHNob3dTYXZlQnV0dG9ucz86IGJvb2xlYW5cclxuICAgIH1cclxuPihcclxuICAgIChcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGdlb0xvY2F0aW9ucyxcclxuICAgICAgICAgICAgc2VsZWN0ZWRFdmVudCA9IGZhbHNlLFxyXG4gICAgICAgICAgICBjbG9zZU1vZGFsLFxyXG4gICAgICAgICAgICBoYW5kbGVTYXZlUGFyZW50LFxyXG4gICAgICAgICAgICBjdXJyZW50UmVzY3VlSUQsXHJcbiAgICAgICAgICAgIHR5cGUsXHJcbiAgICAgICAgICAgIGV2ZW50Q3VycmVudExvY2F0aW9uLFxyXG4gICAgICAgICAgICBsb2NhdGlvbkRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgICBzZXRMb2NhdGlvbkRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgICBvZmZsaW5lID0gZmFsc2UsXHJcbiAgICAgICAgICAgIGxvY2tlZCA9IGZhbHNlLFxyXG4gICAgICAgICAgICBzaG93U2F2ZUJ1dHRvbnMgPSB0cnVlLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgcmVmLFxyXG4gICAgKSA9PiB7XHJcbiAgICAgICAgY29uc3Qgc2VhcmNoUGFyYW1zID0gdXNlU2VhcmNoUGFyYW1zKClcclxuICAgICAgICBjb25zdCBbbG9jYXRpb25zLCBzZXRMb2NhdGlvbnNdID0gdXNlU3RhdGU8YW55PihmYWxzZSlcclxuICAgICAgICBjb25zdCBbdGltZSwgc2V0VGltZV0gPSB1c2VTdGF0ZTxhbnk+KGRheWpzKCkuZm9ybWF0KCdISDptbScpKVxyXG4gICAgICAgIGNvbnN0IFtvcGVuQ29tbWVudHNEaWFsb2csIHNldE9wZW5Db21tZW50c0RpYWxvZ10gPSB1c2VTdGF0ZShmYWxzZSlcclxuICAgICAgICBjb25zdCBbY29tbWVudFRpbWUsIHNldENvbW1lbnRUaW1lXSA9IHVzZVN0YXRlPGFueT4oKVxyXG4gICAgICAgIGNvbnN0IFttZW1iZXJzLCBzZXRNZW1iZXJzXSA9IHVzZVN0YXRlPGFueT4oZmFsc2UpXHJcbiAgICAgICAgY29uc3QgW2NvbnRlbnQsIHNldENvbnRlbnRdID0gdXNlU3RhdGU8YW55PigpXHJcbiAgICAgICAgY29uc3QgW3Jlc2N1ZURhdGEsIHNldFJlc2N1ZURhdGFdID0gdXNlU3RhdGU8YW55PihmYWxzZSlcclxuICAgICAgICBjb25zdCBbbWlzc2lvbkRhdGEsIHNldE1pc3Npb25EYXRhXSA9IHVzZVN0YXRlPGFueT4oZmFsc2UpXHJcbiAgICAgICAgY29uc3QgW2NvbW1lbnREYXRhLCBzZXRDb21tZW50RGF0YV0gPSB1c2VTdGF0ZTxhbnk+KGZhbHNlKVxyXG4gICAgICAgIGNvbnN0IFt0aW1lbGluZSwgc2V0VGltZWxpbmVdID0gdXNlU3RhdGU8YW55PihmYWxzZSlcclxuICAgICAgICBjb25zdCBbZGVsZXRlQ29tbWVudHNEaWFsb2csIHNldERlbGV0ZUNvbW1lbnRzRGlhbG9nXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG4gICAgICAgIGNvbnN0IFthbGxWZXNzZWxDcmV3cywgc2V0QWxsVmVzc2VsQ3Jld3NdID0gdXNlU3RhdGU8Q3Jld01lbWJlcltdPihbXSlcclxuICAgICAgICBjb25zdCBbdmVzc2VsLCBzZXRWZXNzZWxdID0gdXNlU3RhdGU8YW55PigpXHJcbiAgICAgICAgY29uc3QgW21hc3RlcklELCBzZXRNYXN0ZXJJRF0gPSB1c2VTdGF0ZSgwKVxyXG4gICAgICAgIGNvbnN0IFtjcmV3TWVtYmVyT3B0aW9ucywgc2V0Q3Jld01lbWJlck9wdGlvbnNdID0gdXNlU3RhdGU8YW55PihbXSlcclxuICAgICAgICBjb25zdCBbYWxsTWVtYmVycywgc2V0QWxsTWVtYmVyc10gPSB1c2VTdGF0ZTxhbnk+KFtdKVxyXG4gICAgICAgIGNvbnN0IFtjcmV3TWVtYmVycywgc2V0Q3Jld01lbWJlcnNdID0gdXNlU3RhdGU8YW55PihmYWxzZSlcclxuICAgICAgICBjb25zdCBbY3Jld01lbWJlcnNMaXN0LCBzZXRDcmV3TWVtYmVyc0xpc3RdID0gdXNlU3RhdGU8YW55PihbXSlcclxuICAgICAgICBjb25zdCBbbG9nYm9vaywgc2V0TG9nYm9va10gPSB1c2VTdGF0ZTxhbnk+KGZhbHNlKVxyXG4gICAgICAgIGNvbnN0IFtjdXJyZW50TG9jYXRpb24sIHNldEN1cnJlbnRMb2NhdGlvbl0gPSB1c2VTdGF0ZTxhbnk+KHtcclxuICAgICAgICAgICAgbGF0aXR1ZGU6ICcnLFxyXG4gICAgICAgICAgICBsb25naXR1ZGU6ICcnLFxyXG4gICAgICAgIH0pXHJcbiAgICAgICAgY29uc3QgW2N1cnJlbnRNaXNzaW9uTG9jYXRpb24sIHNldEN1cnJlbnRNaXNzaW9uTG9jYXRpb25dID1cclxuICAgICAgICAgICAgdXNlU3RhdGU8YW55Pih7XHJcbiAgICAgICAgICAgICAgICBsYXRpdHVkZTogJycsXHJcbiAgICAgICAgICAgICAgICBsb25naXR1ZGU6ICcnLFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgIGNvbnN0IFtzaG93SW5wdXREZXRhaWxzUCwgc2V0U2hvd0lucHV0RGV0YWlsc1BhbmVsXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG4gICAgICAgIGNvbnN0IGlzV2lkZSA9IHVzZU1lZGlhUXVlcnkoJyhtaW4td2lkdGg6IDY0MHB4KScpXHJcblxyXG4gICAgICAgIGNvbnN0IG1lbWJlck1vZGVsID0gbmV3IFNlYUxvZ3NNZW1iZXJNb2RlbCgpXHJcbiAgICAgICAgY29uc3QgdmVzc2VsUmVzY3VlTW9kZWwgPSBuZXcgRXZlbnRUeXBlX1Zlc3NlbFJlc2N1ZU1vZGVsKClcclxuICAgICAgICBjb25zdCBjZ0V2ZW50TWlzc2lvbk1vZGVsID0gbmV3IENHRXZlbnRNaXNzaW9uTW9kZWwoKVxyXG4gICAgICAgIGNvbnN0IG1pc3Npb25UaW1lbGluZU1vZGVsID0gbmV3IE1pc3Npb25UaW1lbGluZU1vZGVsKClcclxuICAgICAgICBjb25zdCBzZWFMb2dzTWVtYmVyTW9kZWwgPSBuZXcgU2VhTG9nc01lbWJlck1vZGVsKClcclxuICAgICAgICBjb25zdCBjbWxic01vZGVsID0gbmV3IENyZXdNZW1iZXJzX0xvZ0Jvb2tFbnRyeVNlY3Rpb25Nb2RlbCgpXHJcbiAgICAgICAgY29uc3QgbG9nYm9va01vZGVsID0gbmV3IExvZ0Jvb2tFbnRyeU1vZGVsKClcclxuICAgICAgICBjb25zdCBoYW5kbGVUaW1lQ2hhbmdlID0gKGRhdGU6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBzZXRUaW1lKGRheWpzKGRhdGUpLmZvcm1hdCgnSEg6bW0nKSlcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3QgdmVzc2VsSUQgPSBzZWFyY2hQYXJhbXMuZ2V0KCd2ZXNzZWxJRCcpID8/IDBcclxuICAgICAgICBjb25zdCBsb2dlbnRyeUlEID0gc2VhcmNoUGFyYW1zLmdldCgnbG9nZW50cnlJRCcpID8/IDBcclxuICAgICAgICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcclxuXHJcbiAgICAgICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICAgICAgc2V0UmVzY3VlRGF0YShmYWxzZSlcclxuICAgICAgICAgICAgaWYgKGN1cnJlbnRSZXNjdWVJRCkge1xyXG4gICAgICAgICAgICAgICAgZ2V0Q3VycmVudEV2ZW50KGN1cnJlbnRSZXNjdWVJRClcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0sIFtjdXJyZW50UmVzY3VlSURdKVxyXG5cclxuICAgICAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgICAgICBzZXRDdXJyZW50TG9jYXRpb24oZXZlbnRDdXJyZW50TG9jYXRpb24/LmN1cnJlbnRMb2NhdGlvbilcclxuICAgICAgICAgICAgaGFuZGxlTG9jYXRpb25DaGFuZ2UoeyB2YWx1ZTogZXZlbnRDdXJyZW50TG9jYXRpb24/Lmdlb0xvY2F0aW9uSUQgfSlcclxuICAgICAgICB9LCBbZXZlbnRDdXJyZW50TG9jYXRpb25dKVxyXG5cclxuICAgICAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgICAgICBpZiAocmVzY3VlRGF0YSkge1xyXG4gICAgICAgICAgICAgICAgc2V0UmVzY3VlRGF0YSgocHJldjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgLi4ucHJldiwgbG9jYXRpb25EZXNjcmlwdGlvbiB9XHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSwgW2xvY2F0aW9uRGVzY3JpcHRpb25dKVxyXG5cclxuICAgICAgICBjb25zdCBnZXRDdXJyZW50RXZlbnQgPSBhc3luYyAoY3VycmVudFJlc2N1ZUlEOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgaWYgKGN1cnJlbnRSZXNjdWVJRCA+IDApIHtcclxuICAgICAgICAgICAgICAgIGlmIChvZmZsaW5lKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZXZlbnQgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCB2ZXNzZWxSZXNjdWVNb2RlbC5nZXRCeUlkKGN1cnJlbnRSZXNjdWVJRClcclxuICAgICAgICAgICAgICAgICAgICBpZiAoZXZlbnQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0UmVzY3VlRGF0YSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWxOYW1lOiBldmVudD8udmVzc2VsTmFtZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gZXZlbnQ/LnZlc3NlbE5hbWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FsbFNpZ246IGV2ZW50Py5jYWxsU2lnbiA/IGV2ZW50Py5jYWxsU2lnbiA6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcG9iOiBldmVudD8ucG9iID8gZXZlbnQ/LnBvYiA6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGF0aXR1ZGU6IGV2ZW50Py5sYXRpdHVkZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gZXZlbnQ/LmxhdGl0dWRlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBldmVudEN1cnJlbnRMb2NhdGlvbj8ubGF0aXR1ZGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb25naXR1ZGU6IGV2ZW50Py5sb25naXR1ZGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGV2ZW50Py5sb25naXR1ZGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGV2ZW50Q3VycmVudExvY2F0aW9uPy5sb25naXR1ZGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NhdGlvbkRlc2NyaXB0aW9uOiBldmVudD8ubG9jYXRpb25EZXNjcmlwdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gZXZlbnQ/LmxvY2F0aW9uRGVzY3JpcHRpb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsTGVuZ3RoOiBldmVudD8udmVzc2VsTGVuZ3RoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBldmVudD8udmVzc2VsTGVuZ3RoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbFR5cGU6IGV2ZW50Py52ZXNzZWxUeXBlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBldmVudD8udmVzc2VsVHlwZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYWtlQW5kTW9kZWw6IGV2ZW50Py5tYWtlQW5kTW9kZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGV2ZW50Py5tYWtlQW5kTW9kZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IGV2ZW50Py5jb2xvciA/IGV2ZW50Py5jb2xvciA6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb3duZXJOYW1lOiBldmVudD8ub3duZXJOYW1lID8gZXZlbnQ/Lm93bmVyTmFtZSA6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGhvbmU6IGV2ZW50Py5waG9uZSA/IGV2ZW50Py5waG9uZSA6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZW1haWw6IGV2ZW50Py5lbWFpbCA/IGV2ZW50Py5lbWFpbCA6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWRkcmVzczogZXZlbnQ/LmFkZHJlc3MgPyBldmVudD8uYWRkcmVzcyA6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb3duZXJPbkJvYXJkOiBldmVudD8ub3duZXJPbkJvYXJkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBldmVudD8ub3duZXJPbkJvYXJkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBmYWxzZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNnTWVtYmVyc2hpcDogZXZlbnQ/LmNnTWVtYmVyc2hpcFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gZXZlbnQ/LmNnTWVtYmVyc2hpcFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NhdGlvbklEOiBldmVudD8udmVzc2VsTG9jYXRpb25JRFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gZXZlbnQ/LnZlc3NlbExvY2F0aW9uSURcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGV2ZW50Q3VycmVudExvY2F0aW9uPy5nZW9Mb2NhdGlvbklELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWlzc2lvbklEOiBldmVudD8ubWlzc2lvbj8uaWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGV2ZW50Py5taXNzaW9uPy5pZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcGVyYXRpb25UeXBlOiBldmVudD8ub3BlcmF0aW9uVHlwZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gb3BlcmF0aW9uVHlwZS5maWx0ZXIoKG9wZXJhdGlvbjogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV2ZW50Py5vcGVyYXRpb25UeXBlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5zcGxpdCgnLCcpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5pbmNsdWRlcyhvcGVyYXRpb24udmFsdWUpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogW10sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcGVyYXRpb25EZXNjcmlwdGlvbjogZXZlbnQ/Lm9wZXJhdGlvbkRlc2NyaXB0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBldmVudD8ub3BlcmF0aW9uRGVzY3JpcHRpb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsVHlwZURlc2NyaXB0aW9uOiBldmVudD8udmVzc2VsVHlwZURlc2NyaXB0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBldmVudD8udmVzc2VsVHlwZURlc2NyaXB0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0VGltZShldmVudD8ubWlzc2lvbj8uY29tcGxldGVkQXQpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldE1pc3Npb25EYXRhKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pc3Npb25UeXBlOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV2ZW50Py5taXNzaW9uPy5taXNzaW9uVHlwZT8ucmVwbGFjZUFsbChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ18nLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnICcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBldmVudD8ubWlzc2lvbj8uZGVzY3JpcHRpb24sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcGVyYXRpb25PdXRjb21lOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV2ZW50Py5taXNzaW9uPy5vcGVyYXRpb25PdXRjb21lPy5yZXBsYWNlQWxsKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnXycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICcgJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudExvY2F0aW9uSUQ6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXZlbnQ/Lm1pc3Npb24/LmN1cnJlbnRMb2NhdGlvbj8uaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcGVyYXRpb25EZXNjcmlwdGlvbjpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBldmVudD8ubWlzc2lvbj8ub3BlcmF0aW9uRGVzY3JpcHRpb24sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYXQ6IGV2ZW50Py5taXNzaW9uPy5jdXJyZW50TG9jYXRpb24/LmxhdCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvbmc6IGV2ZW50Py5taXNzaW9uPy5jdXJyZW50TG9jYXRpb24/LmxvbmcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFRpbWVsaW5lKGV2ZW50Py5taXNzaW9uVGltZWxpbmU/Lm5vZGVzKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRDdXJyZW50TG9jYXRpb24oe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGF0aXR1ZGU6IGV2ZW50Py5taXNzaW9uPy5jdXJyZW50TG9jYXRpb24/LmxhdCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvbmdpdHVkZTogZXZlbnQ/Lm1pc3Npb24/LmN1cnJlbnRMb2NhdGlvbj8ubG9uZyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0Q3VycmVudE1pc3Npb25Mb2NhdGlvbih7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYXRpdHVkZTogZXZlbnQ/LmxhdCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvbmdpdHVkZTogZXZlbnQ/LmxvbmcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldExvY2F0aW9uRGVzY3JpcHRpb24oZXZlbnQ/LmxvY2F0aW9uRGVzY3JpcHRpb24pXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICBnZXRUcmlwRXZlbnQoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkOiArY3VycmVudFJlc2N1ZUlELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IFtnZXRUcmlwRXZlbnRdID0gdXNlTGF6eVF1ZXJ5KEdldFRyaXBFdmVudF9WZXNzZWxSZXNjdWUsIHtcclxuICAgICAgICAgICAgZmV0Y2hQb2xpY3k6ICdjYWNoZS1hbmQtbmV0d29yaycsXHJcbiAgICAgICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2UpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGV2ZW50ID0gcmVzcG9uc2UucmVhZE9uZUV2ZW50VHlwZV9WZXNzZWxSZXNjdWVcclxuICAgICAgICAgICAgICAgIGlmIChldmVudCkge1xyXG4gICAgICAgICAgICAgICAgICAgIHNldFJlc2N1ZURhdGEoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWxOYW1lOiBldmVudD8udmVzc2VsTmFtZSA/IGV2ZW50Py52ZXNzZWxOYW1lIDogJycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhbGxTaWduOiBldmVudD8uY2FsbFNpZ24gPyBldmVudD8uY2FsbFNpZ24gOiAnJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgcG9iOiBldmVudD8ucG9iID8gZXZlbnQ/LnBvYiA6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBsYXRpdHVkZTogZXZlbnQ/LmxhdGl0dWRlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGV2ZW50Py5sYXRpdHVkZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBldmVudEN1cnJlbnRMb2NhdGlvbj8ubGF0aXR1ZGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxvbmdpdHVkZTogZXZlbnQ/LmxvbmdpdHVkZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBldmVudD8ubG9uZ2l0dWRlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGV2ZW50Q3VycmVudExvY2F0aW9uPy5sb25naXR1ZGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxvY2F0aW9uRGVzY3JpcHRpb246IGV2ZW50Py5sb2NhdGlvbkRlc2NyaXB0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGV2ZW50Py5sb2NhdGlvbkRlc2NyaXB0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWxMZW5ndGg6IGV2ZW50Py52ZXNzZWxMZW5ndGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gZXZlbnQ/LnZlc3NlbExlbmd0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsVHlwZTogZXZlbnQ/LnZlc3NlbFR5cGUgPyBldmVudD8udmVzc2VsVHlwZSA6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtYWtlQW5kTW9kZWw6IGV2ZW50Py5tYWtlQW5kTW9kZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gZXZlbnQ/Lm1ha2VBbmRNb2RlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IGV2ZW50Py5jb2xvciA/IGV2ZW50Py5jb2xvciA6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvd25lck5hbWU6IGV2ZW50Py5vd25lck5hbWUgPyBldmVudD8ub3duZXJOYW1lIDogJycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBob25lOiBldmVudD8ucGhvbmUgPyBldmVudD8ucGhvbmUgOiAnJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgZW1haWw6IGV2ZW50Py5lbWFpbCA/IGV2ZW50Py5lbWFpbCA6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBhZGRyZXNzOiBldmVudD8uYWRkcmVzcyA/IGV2ZW50Py5hZGRyZXNzIDogJycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG93bmVyT25Cb2FyZDogZXZlbnQ/Lm93bmVyT25Cb2FyZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBldmVudD8ub3duZXJPbkJvYXJkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGZhbHNlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjZ01lbWJlcnNoaXA6IGV2ZW50Py5jZ01lbWJlcnNoaXBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gZXZlbnQ/LmNnTWVtYmVyc2hpcFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbG9jYXRpb25JRDogZXZlbnQ/LnZlc3NlbExvY2F0aW9uSURcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gZXZlbnQ/LnZlc3NlbExvY2F0aW9uSURcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogZXZlbnRDdXJyZW50TG9jYXRpb24/Lmdlb0xvY2F0aW9uSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1pc3Npb25JRDogZXZlbnQ/Lm1pc3Npb24/LmlkID8gZXZlbnQ/Lm1pc3Npb24/LmlkIDogJycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9wZXJhdGlvblR5cGU6IGV2ZW50Py5vcGVyYXRpb25UeXBlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IG9wZXJhdGlvblR5cGUuZmlsdGVyKChvcGVyYXRpb246IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV2ZW50Py5vcGVyYXRpb25UeXBlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLnNwbGl0KCcsJylcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuaW5jbHVkZXMob3BlcmF0aW9uLnZhbHVlKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBbXSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgb3BlcmF0aW9uRGVzY3JpcHRpb246IGV2ZW50Py5vcGVyYXRpb25EZXNjcmlwdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBldmVudD8ub3BlcmF0aW9uRGVzY3JpcHRpb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbFR5cGVEZXNjcmlwdGlvbjogZXZlbnQ/LnZlc3NlbFR5cGVEZXNjcmlwdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBldmVudD8udmVzc2VsVHlwZURlc2NyaXB0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0VGltZShldmVudD8ubWlzc2lvbj8uY29tcGxldGVkQXQpXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0TWlzc2lvbkRhdGEoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBtaXNzaW9uVHlwZTogZXZlbnQ/Lm1pc3Npb24/Lm1pc3Npb25UeXBlPy5yZXBsYWNlQWxsKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJ18nLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJyAnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogZXZlbnQ/Lm1pc3Npb24/LmRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvcGVyYXRpb25PdXRjb21lOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXZlbnQ/Lm1pc3Npb24/Lm9wZXJhdGlvbk91dGNvbWU/LnJlcGxhY2VBbGwoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ18nLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICcgJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRMb2NhdGlvbklEOiBldmVudD8ubWlzc2lvbj8uY3VycmVudExvY2F0aW9uPy5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgb3BlcmF0aW9uRGVzY3JpcHRpb246XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBldmVudD8ubWlzc2lvbj8ub3BlcmF0aW9uRGVzY3JpcHRpb24sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxhdDogZXZlbnQ/Lm1pc3Npb24/LmN1cnJlbnRMb2NhdGlvbj8ubGF0LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBsb25nOiBldmVudD8ubWlzc2lvbj8uY3VycmVudExvY2F0aW9uPy5sb25nLFxyXG4gICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0VGltZWxpbmUoZXZlbnQ/Lm1pc3Npb25UaW1lbGluZT8ubm9kZXMpXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3VycmVudExvY2F0aW9uKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbGF0aXR1ZGU6IGV2ZW50Py5taXNzaW9uPy5jdXJyZW50TG9jYXRpb24/LmxhdCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbG9uZ2l0dWRlOiBldmVudD8ubWlzc2lvbj8uY3VycmVudExvY2F0aW9uPy5sb25nLFxyXG4gICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3VycmVudE1pc3Npb25Mb2NhdGlvbih7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxhdGl0dWRlOiBldmVudD8ubGF0LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBsb25naXR1ZGU6IGV2ZW50Py5sb25nLFxyXG4gICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0TG9jYXRpb25EZXNjcmlwdGlvbihldmVudD8ubG9jYXRpb25EZXNjcmlwdGlvbilcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIGN1cnJlbnQgZXZlbnQnLCBlcnJvcilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9KVxyXG5cclxuICAgICAgICBjb25zdCBoYW5kbGVTZXRNZW1iZXJMaXN0ID0gKG1lbWJlcnM6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBzZXRNZW1iZXJzKFxyXG4gICAgICAgICAgICAgICAgbWVtYmVyc1xyXG4gICAgICAgICAgICAgICAgICAgID8uZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAobWVtYmVyOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXIuYXJjaGl2ZWQgPT0gZmFsc2UgJiYgbWVtYmVyLmZpcnN0TmFtZSAhPSAnJyxcclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgPy5tYXAoKG1lbWJlcjogYW55KSA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogbWVtYmVyLmZpcnN0TmFtZSArICcgJyArIG1lbWJlci5zdXJuYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogbWVtYmVyLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgIH0pKSxcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgZ2V0U2VhTG9nc01lbWJlcnNMaXN0KGhhbmRsZVNldE1lbWJlckxpc3QsIG9mZmxpbmUpXHJcblxyXG4gICAgICAgIGNvbnN0IGhhbmRsZUNvbW1lbnRUaW1lQ2hhbmdlID0gKGRhdGU6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBzZXRDb21tZW50VGltZShkYXlqcyhkYXRlKS5mb3JtYXQoJ0hIOm1tJykpXHJcbiAgICAgICAgICAgIC8vIHNldENvbW1lbnRUaW1lKGRhdGUpXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgICAgICBpZiAoZ2VvTG9jYXRpb25zKSB7XHJcbiAgICAgICAgICAgICAgICBzZXRMb2NhdGlvbnMoXHJcbiAgICAgICAgICAgICAgICAgICAgZ2VvTG9jYXRpb25zLm1hcCgobG9jYXRpb246IGFueSkgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IGxvY2F0aW9uLnRpdGxlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogbG9jYXRpb24uaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxhdGl0dWRlOiBsb2NhdGlvbi5sYXQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxvbmdpdHVkZTogbG9jYXRpb24ubG9uZyxcclxuICAgICAgICAgICAgICAgICAgICB9KSksXHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9LCBbZ2VvTG9jYXRpb25zXSlcclxuXHJcbiAgICAgICAgY29uc3QgdmVzc2VsVHlwZXMgPSBbXHJcbiAgICAgICAgICAgIHsgbGFiZWw6ICdDb21tZXJjaWFsJywgdmFsdWU6ICdDb21tZXJjaWFsJyB9LFxyXG4gICAgICAgICAgICB7IGxhYmVsOiAnUmVjcmVhdGlvbicsIHZhbHVlOiAnUmVjcmVhdGlvbicgfSxcclxuICAgICAgICAgICAgLy8geyBsYWJlbDogJ1Bvd2VyJywgdmFsdWU6ICdQb3dlcicgfSxcclxuICAgICAgICAgICAgeyBsYWJlbDogJ1NhaWwnLCB2YWx1ZTogJ1NhaWwnIH0sXHJcbiAgICAgICAgICAgIHsgbGFiZWw6ICdQYWRkbGUgY3JhZnRzJywgdmFsdWU6ICdQYWRkbGUgY3JhZnRzJyB9LFxyXG4gICAgICAgICAgICB7IGxhYmVsOiAnUFdDJywgdmFsdWU6ICdQV0MnIH0sXHJcbiAgICAgICAgICAgIHsgbGFiZWw6ICdPdGhlcicsIHZhbHVlOiAnT3RoZXInIH0sXHJcbiAgICAgICAgXVxyXG5cclxuICAgICAgICBjb25zdCBtaXNzaW9ucyA9IFtcclxuICAgICAgICAgICAgeyBsYWJlbDogJ1RvIGxvY2F0ZScsIHZhbHVlOiAnVG8gbG9jYXRlJyB9LFxyXG4gICAgICAgICAgICB7IGxhYmVsOiAnVG8gYXNzaXN0JywgdmFsdWU6ICdUbyBhc3Npc3QnIH0sXHJcbiAgICAgICAgICAgIHsgbGFiZWw6ICdUbyBzYXZlJywgdmFsdWU6ICdUbyBzYXZlJyB9LFxyXG4gICAgICAgICAgICB7IGxhYmVsOiAnVG8gcmVzY3VlJywgdmFsdWU6ICdUbyByZXNjdWUnIH0sXHJcbiAgICAgICAgICAgIHsgbGFiZWw6ICdUbyByZW1vdmUnLCB2YWx1ZTogJ1RvIHJlbW92ZScgfSxcclxuICAgICAgICBdXHJcblxyXG4gICAgICAgIGNvbnN0IG9wZXJhdGlvbk91dGNvbWVzID0gW1xyXG4gICAgICAgICAgICB7IGxhYmVsOiAnQXNzaXN0ZWQgYnkgb3RoZXJzJywgdmFsdWU6ICdBc3Npc3RlZCBieSBvdGhlcnMnIH0sXHJcbiAgICAgICAgICAgIHsgbGFiZWw6ICdBc3Npc3RlZCBvbiBzY2VuZScsIHZhbHVlOiAnQXNzaXN0ZWQgb24gc2NlbmUnIH0sXHJcbiAgICAgICAgICAgIHsgbGFiZWw6ICdNZWRpY2FsIHRyZWF0bWVudCcsIHZhbHVlOiAnTWVkaWNhbCB0cmVhdG1lbnQnIH0sXHJcbiAgICAgICAgICAgIHsgbGFiZWw6ICdTYWZlIGFuZCB3ZWxsJywgdmFsdWU6ICdTYWZlIGFuZCB3ZWxsJyB9LFxyXG4gICAgICAgICAgICB7IGxhYmVsOiAnTm90IGxvY2F0ZWQnLCB2YWx1ZTogJ05vdCBsb2NhdGVkJyB9LFxyXG4gICAgICAgICAgICB7IGxhYmVsOiAnTm90IHJlY292ZXJhYmxlJywgdmFsdWU6ICdOb3QgcmVjb3ZlcmFibGUnIH0sXHJcbiAgICAgICAgICAgIHsgbGFiZWw6ICdGYXRhbGl0eScsIHZhbHVlOiAnRmF0YWxpdHknIH0sXHJcbiAgICAgICAgICAgIHsgbGFiZWw6ICdTdG9vZCBkb3duJywgdmFsdWU6ICdTdG9vZCBkb3duJyB9LFxyXG4gICAgICAgICAgICB7IGxhYmVsOiAnT3RoZXInLCB2YWx1ZTogJ090aGVyJyB9LFxyXG4gICAgICAgIF1cclxuXHJcbiAgICAgICAgY29uc3QgY29tbWVudFR5cGVzID0gW1xyXG4gICAgICAgICAgICB7IGxhYmVsOiAnR2VuZXJhbCcsIHZhbHVlOiAnR2VuZXJhbCcgfSxcclxuICAgICAgICAgICAgeyBsYWJlbDogJ1VuZGVyd2F5JywgdmFsdWU6ICdVbmRlcndheScgfSxcclxuICAgICAgICAgICAgeyBsYWJlbDogJ09uIFNjZW5lJywgdmFsdWU6ICdPbiBTY2VuZScgfSxcclxuICAgICAgICBdXHJcblxyXG4gICAgICAgIGNvbnN0IG9wZXJhdGlvblR5cGUgPSBbXHJcbiAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgIGxhYmVsOiAnTWVjaGFuaWNhbCAvIGVxdWlwbWVudCBmYWlsdXJlJyxcclxuICAgICAgICAgICAgICAgIHZhbHVlOiAnTWVjaGFuaWNhbCAvIGVxdWlwbWVudCBmYWlsdXJlJyxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgeyBsYWJlbDogJ1Zlc3NlbCBhZHJpZnQnLCB2YWx1ZTogJ1Zlc3NlbCBhZHJpZnQnIH0sXHJcbiAgICAgICAgICAgIHsgbGFiZWw6ICdWZXNzZWwgYWdyb3VuZCcsIHZhbHVlOiAnVmVzc2VsIGFncm91bmQnIH0sXHJcbiAgICAgICAgICAgIHsgbGFiZWw6ICdDYXBzaXplJywgdmFsdWU6ICdDYXBzaXplJyB9LFxyXG4gICAgICAgICAgICB7IGxhYmVsOiAnVmVzc2VsIHJlcXVpcmluZyB0b3cnLCB2YWx1ZTogJ1Zlc3NlbCByZXF1aXJpbmcgdG93JyB9LFxyXG4gICAgICAgICAgICB7IGxhYmVsOiAnRmxhcmUgc2lnaHRpbmcnLCB2YWx1ZTogJ0ZsYXJlIHNpZ2h0aW5nJyB9LFxyXG4gICAgICAgICAgICB7IGxhYmVsOiAnVmVzc2VsIHNpbmtpbmcnLCB2YWx1ZTogJ1Zlc3NlbCBzaW5raW5nJyB9LFxyXG4gICAgICAgICAgICB7IGxhYmVsOiAnQ29sbGlzaW9uJywgdmFsdWU6ICdDb2xsaXNpb24nIH0sXHJcbiAgICAgICAgICAgIHsgbGFiZWw6ICdWZXNzZWwgb3ZlcmR1ZScsIHZhbHVlOiAnVmVzc2VsIG92ZXJkdWUnIH0sXHJcbiAgICAgICAgICAgIHsgbGFiZWw6ICdPdGhlcicsIHZhbHVlOiAnT3RoZXInIH0sXHJcbiAgICAgICAgXVxyXG5cclxuICAgICAgICBjb25zdCBoYW5kbGVTYXZlQ29tbWVudHMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICAgIGlmIChyZXNjdWVEYXRhPy5taXNzaW9uSUQgPT09IHVuZGVmaW5lZCkge1xyXG4gICAgICAgICAgICAgICAgdG9hc3QuZXJyb3IoXHJcbiAgICAgICAgICAgICAgICAgICAgJ1BsZWFzZSBzYXZlIHRoZSBldmVudCBmaXJzdCBpbiBvcmRlciB0byBjcmVhdGUgdGltZWxpbmUhJyxcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgIHNldE9wZW5Db21tZW50c0RpYWxvZyhmYWxzZSlcclxuICAgICAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGNvbnN0IHZhcmlhYmxlcyA9IHtcclxuICAgICAgICAgICAgICAgIGlucHV0OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29tbWVudFR5cGU6IGNvbW1lbnREYXRhPy5jb21tZW50VHlwZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGNvbW1lbnREYXRhPy5jb21tZW50VHlwZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6ICdHZW5lcmFsJyxcclxuICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogY29udGVudCA/IGNvbnRlbnQgOiAnJyxcclxuICAgICAgICAgICAgICAgICAgICB0aW1lOiBjb21tZW50VGltZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGRheWpzKCkuZm9ybWF0KCdERC9NTS9ZWVlZJykgKyAnICcgKyBjb21tZW50VGltZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IGRheWpzKCkuZm9ybWF0KCdERC9NTS9ZWVlZIEhIOm1tJyksXHJcbiAgICAgICAgICAgICAgICAgICAgYXV0aG9ySUQ6IGNvbW1lbnREYXRhPy5hdXRob3JJRCxcclxuICAgICAgICAgICAgICAgICAgICAvLyBtaXNzaW9uSUQ6IHJlc2N1ZURhdGE/Lm1pc3Npb25JRCxcclxuICAgICAgICAgICAgICAgICAgICB2ZXNzZWxSZXNjdWVJRDogY3VycmVudFJlc2N1ZUlELFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgaWYgKGNvbW1lbnREYXRhPy5pZCA+IDApIHtcclxuICAgICAgICAgICAgICAgIGlmIChvZmZsaW5lKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgbWlzc2lvblRpbWVsaW5lTW9kZWwuc2F2ZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBjb21tZW50RGF0YT8uaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLnZhcmlhYmxlcy5pbnB1dCxcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgIC8vIHRvYXN0LnN1Y2Nlc3MoJ01pc3Npb24gdGltZWxpbmUgdXBkYXRlZCcpXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0T3BlbkNvbW1lbnRzRGlhbG9nKGZhbHNlKVxyXG4gICAgICAgICAgICAgICAgICAgIHNldERlbGV0ZUNvbW1lbnRzRGlhbG9nKGZhbHNlKVxyXG4gICAgICAgICAgICAgICAgICAgIGdldEN1cnJlbnRFdmVudChjdXJyZW50UmVzY3VlSUQpXHJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIHVwZGF0ZU1pc3Npb25UaW1lbGluZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5wdXQ6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogY29tbWVudERhdGE/LmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnZhcmlhYmxlcy5pbnB1dCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGlmIChvZmZsaW5lKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgbWlzc2lvblRpbWVsaW5lTW9kZWwuc2F2ZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBnZW5lcmF0ZVVuaXF1ZUlkKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLnZhcmlhYmxlcy5pbnB1dCxcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgIC8vIHRvYXN0LnN1Y2Nlc3MoJ01pc3Npb24gdGltZWxpbmUgY3JlYXRlZCcpXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0T3BlbkNvbW1lbnRzRGlhbG9nKGZhbHNlKVxyXG4gICAgICAgICAgICAgICAgICAgIHNldERlbGV0ZUNvbW1lbnRzRGlhbG9nKGZhbHNlKVxyXG4gICAgICAgICAgICAgICAgICAgIGF3YWl0IGdldEN1cnJlbnRFdmVudChjdXJyZW50UmVzY3VlSUQpXHJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIGNyZWF0ZU1pc3Npb25UaW1lbGluZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5wdXQ6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi52YXJpYWJsZXMuaW5wdXQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgc2V0T3BlbkNvbW1lbnRzRGlhbG9nKGZhbHNlKVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgW2NyZWF0ZU1pc3Npb25UaW1lbGluZV0gPSB1c2VNdXRhdGlvbihDcmVhdGVNaXNzaW9uVGltZWxpbmUsIHtcclxuICAgICAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgLy8gdG9hc3Quc3VjY2VzcygnTWlzc2lvbiB0aW1lbGluZSBjcmVhdGVkJylcclxuICAgICAgICAgICAgICAgIHNldE9wZW5Db21tZW50c0RpYWxvZyhmYWxzZSlcclxuICAgICAgICAgICAgICAgIHNldERlbGV0ZUNvbW1lbnRzRGlhbG9nKGZhbHNlKVxyXG4gICAgICAgICAgICAgICAgZ2V0Q3VycmVudEV2ZW50KGN1cnJlbnRSZXNjdWVJRClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyBtaXNzaW9uIHRpbWVsaW5lJywgZXJyb3IpXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSlcclxuXHJcbiAgICAgICAgY29uc3QgW3VwZGF0ZU1pc3Npb25UaW1lbGluZV0gPSB1c2VNdXRhdGlvbihVcGRhdGVNaXNzaW9uVGltZWxpbmUsIHtcclxuICAgICAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgLy8gdG9hc3Quc3VjY2VzcygnTWlzc2lvbiB0aW1lbGluZSB1cGRhdGVkJylcclxuICAgICAgICAgICAgICAgIHNldE9wZW5Db21tZW50c0RpYWxvZyhmYWxzZSlcclxuICAgICAgICAgICAgICAgIHNldERlbGV0ZUNvbW1lbnRzRGlhbG9nKGZhbHNlKVxyXG4gICAgICAgICAgICAgICAgZ2V0Q3VycmVudEV2ZW50KGN1cnJlbnRSZXNjdWVJRClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBtaXNzaW9uIHRpbWVsaW5lJywgZXJyb3IpXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSlcclxuXHJcbiAgICAgICAgY29uc3QgaGFuZGxlRWRpdG9yQ2hhbmdlID0gKG5ld0NvbnRlbnQ6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBzZXRDb250ZW50KG5ld0NvbnRlbnQpXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCBoYW5kbGVTYXZlID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCB2YXJpYWJsZXMgPSB7XHJcbiAgICAgICAgICAgICAgICBpbnB1dDoge1xyXG4gICAgICAgICAgICAgICAgICAgIHZlc3NlbE5hbWU6IHJlc2N1ZURhdGEudmVzc2VsTmFtZSxcclxuICAgICAgICAgICAgICAgICAgICBjYWxsU2lnbjogcmVzY3VlRGF0YS5jYWxsU2lnbixcclxuICAgICAgICAgICAgICAgICAgICBwb2I6ICtyZXNjdWVEYXRhLnBvYixcclxuICAgICAgICAgICAgICAgICAgICBsYXRpdHVkZTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzY3VlRGF0YS5sYXRpdHVkZSA+IDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gcmVzY3VlRGF0YS5sYXRpdHVkZT8udG9TdHJpbmcoKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBjdXJyZW50TG9jYXRpb24ubGF0aXR1ZGU/LnRvU3RyaW5nKCksXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIGxvbmdpdHVkZTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzY3VlRGF0YS5sb25naXR1ZGUgPiAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHJlc2N1ZURhdGEubG9uZ2l0dWRlPy50b1N0cmluZygpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGN1cnJlbnRMb2NhdGlvbi5sb25naXR1ZGU/LnRvU3RyaW5nKCksXHJcbiAgICAgICAgICAgICAgICAgICAgbG9jYXRpb25EZXNjcmlwdGlvbjogcmVzY3VlRGF0YS5sb2NhdGlvbkRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgIHZlc3NlbExlbmd0aDogK3Jlc2N1ZURhdGEudmVzc2VsTGVuZ3RoLFxyXG4gICAgICAgICAgICAgICAgICAgIHZlc3NlbFR5cGU6IHJlc2N1ZURhdGEudmVzc2VsVHlwZSxcclxuICAgICAgICAgICAgICAgICAgICBtYWtlQW5kTW9kZWw6IHJlc2N1ZURhdGEubWFrZUFuZE1vZGVsLFxyXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiByZXNjdWVEYXRhLmNvbG9yLFxyXG4gICAgICAgICAgICAgICAgICAgIG93bmVyTmFtZTogcmVzY3VlRGF0YS5vd25lck5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgcGhvbmU6IHJlc2N1ZURhdGEucGhvbmUsXHJcbiAgICAgICAgICAgICAgICAgICAgZW1haWw6IHJlc2N1ZURhdGEuZW1haWwsXHJcbiAgICAgICAgICAgICAgICAgICAgYWRkcmVzczogcmVzY3VlRGF0YS5hZGRyZXNzLFxyXG4gICAgICAgICAgICAgICAgICAgIG93bmVyT25Cb2FyZDogcmVzY3VlRGF0YS5vd25lck9uQm9hcmQsXHJcbiAgICAgICAgICAgICAgICAgICAgY2dNZW1iZXJzaGlwVHlwZTogJ2NnbnonLFxyXG4gICAgICAgICAgICAgICAgICAgIGNnTWVtYmVyc2hpcDogcmVzY3VlRGF0YS5jZ01lbWJlcnNoaXAsXHJcbiAgICAgICAgICAgICAgICAgICAgbWlzc2lvbklEOiByZXNjdWVEYXRhLm1pc3Npb25JRCxcclxuICAgICAgICAgICAgICAgICAgICB2ZXNzZWxMb2NhdGlvbklEOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXNjdWVEYXRhLmxvY2F0aW9uSUQgPiAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHJlc2N1ZURhdGEubG9jYXRpb25JRFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBldmVudEN1cnJlbnRMb2NhdGlvbi5nZW9Mb2NhdGlvbklELFxyXG4gICAgICAgICAgICAgICAgICAgIG9wZXJhdGlvblR5cGU6IHJlc2N1ZURhdGEub3BlcmF0aW9uVHlwZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/Lm1hcCgodHlwZTogYW55KSA9PiB0eXBlLnZhbHVlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuam9pbignLCcpLFxyXG4gICAgICAgICAgICAgICAgICAgIG9wZXJhdGlvbkRlc2NyaXB0aW9uOiByZXNjdWVEYXRhLm9wZXJhdGlvbkRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgIHZlc3NlbFR5cGVEZXNjcmlwdGlvbjogcmVzY3VlRGF0YS52ZXNzZWxUeXBlRGVzY3JpcHRpb24sXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBpZiAoY3VycmVudFJlc2N1ZUlEID4gMCkge1xyXG4gICAgICAgICAgICAgICAgaWYgKG9mZmxpbmUpIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgdmVzc2VsUmVzY3VlTW9kZWwuc2F2ZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkOiArY3VycmVudFJlc2N1ZUlELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi52YXJpYWJsZXMuaW5wdXQsXHJcbiAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICBpZiAocmVzY3VlRGF0YS5taXNzaW9uSUQgPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGF3YWl0IGNnRXZlbnRNaXNzaW9uTW9kZWwuc2F2ZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogcmVzY3VlRGF0YS5taXNzaW9uSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaXNzaW9uVHlwZTogbWlzc2lvbkRhdGEubWlzc2lvblR5cGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogbWlzc2lvbkRhdGEuZGVzY3JpcHRpb24sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcGVyYXRpb25EZXNjcmlwdGlvbjpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaXNzaW9uRGF0YS5vcGVyYXRpb25EZXNjcmlwdGlvbixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wZXJhdGlvbk91dGNvbWU6IG1pc3Npb25EYXRhLm9wZXJhdGlvbk91dGNvbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb21wbGV0ZWRBdDogdGltZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRMb2NhdGlvbklEOiByZXNjdWVEYXRhLmxvY2F0aW9uSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBldmVudElEOiArZGF0YT8uaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBldmVudFR5cGU6ICdWZXNzZWxSZXNjdWUnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGF0OiBjdXJyZW50TWlzc2lvbkxvY2F0aW9uLmxhdGl0dWRlPy50b1N0cmluZygpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9uZzogY3VycmVudE1pc3Npb25Mb2NhdGlvbi5sb25naXR1ZGU/LnRvU3RyaW5nKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgY2dFdmVudE1pc3Npb25Nb2RlbC5zYXZlKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBnZW5lcmF0ZVVuaXF1ZUlkKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaXNzaW9uVHlwZTogbWlzc2lvbkRhdGEubWlzc2lvblR5cGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogbWlzc2lvbkRhdGEuZGVzY3JpcHRpb24sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcGVyYXRpb25EZXNjcmlwdGlvbjpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaXNzaW9uRGF0YS5vcGVyYXRpb25EZXNjcmlwdGlvbixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wZXJhdGlvbk91dGNvbWU6IG1pc3Npb25EYXRhLm9wZXJhdGlvbk91dGNvbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb21wbGV0ZWRBdDogdGltZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRMb2NhdGlvbklEOiByZXNjdWVEYXRhLmN1cnJlbnRMb2NhdGlvbklELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXZlbnRJRDogK2RhdGE/LmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXZlbnRUeXBlOiAnVmVzc2VsUmVzY3VlJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhdDpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50TWlzc2lvbkxvY2F0aW9uLmxhdGl0dWRlPy50b1N0cmluZygpID8/XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbnVsbCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvbmc6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudE1pc3Npb25Mb2NhdGlvbi5sb25naXR1ZGU/LnRvU3RyaW5nKCkgPz9cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBudWxsLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICBoYW5kbGVTYXZlUGFyZW50KCtjdXJyZW50UmVzY3VlSUQsIDApXHJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIHVwZGF0ZUV2ZW50VHlwZV9WZXNzZWxSZXNjdWUoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6ICtjdXJyZW50UmVzY3VlSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4udmFyaWFibGVzLmlucHV0LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgaWYgKG9mZmxpbmUpIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgdmVzc2VsUmVzY3VlTW9kZWwuc2F2ZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBnZW5lcmF0ZVVuaXF1ZUlkKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbE5hbWU6IHJlc2N1ZURhdGEudmVzc2VsTmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2FsbFNpZ246IHJlc2N1ZURhdGEuY2FsbFNpZ24sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBvYjogK3Jlc2N1ZURhdGEucG9iLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBsYXRpdHVkZTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlc2N1ZURhdGEubGF0aXR1ZGUgPiAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyByZXNjdWVEYXRhLmxhdGl0dWRlPy50b1N0cmluZygpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBjdXJyZW50TG9jYXRpb24ubGF0aXR1ZGU/LnRvU3RyaW5nKCksXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBsb25naXR1ZGU6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXNjdWVEYXRhLmxvbmdpdHVkZSA+IDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHJlc2N1ZURhdGEubG9uZ2l0dWRlPy50b1N0cmluZygpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBjdXJyZW50TG9jYXRpb24ubG9uZ2l0dWRlPy50b1N0cmluZygpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBsb2NhdGlvbkRlc2NyaXB0aW9uOiByZXNjdWVEYXRhLmxvY2F0aW9uRGVzY3JpcHRpb24sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbExlbmd0aDogK3Jlc2N1ZURhdGEudmVzc2VsTGVuZ3RoLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWxUeXBlOiByZXNjdWVEYXRhLnZlc3NlbFR5cGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1ha2VBbmRNb2RlbDogcmVzY3VlRGF0YS5tYWtlQW5kTW9kZWwsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiByZXNjdWVEYXRhLmNvbG9yLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvd25lck5hbWU6IHJlc2N1ZURhdGEub3duZXJOYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwaG9uZTogcmVzY3VlRGF0YS5waG9uZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgZW1haWw6IHJlc2N1ZURhdGEuZW1haWwsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGFkZHJlc3M6IHJlc2N1ZURhdGEuYWRkcmVzcyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgb3duZXJPbkJvYXJkOiByZXNjdWVEYXRhLm93bmVyT25Cb2FyZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2dNZW1iZXJzaGlwVHlwZTogJ2NnbnonLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjZ01lbWJlcnNoaXA6IHJlc2N1ZURhdGEuY2dNZW1iZXJzaGlwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtaXNzaW9uSUQ6IHJlc2N1ZURhdGEubWlzc2lvbklELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWxMb2NhdGlvbklEOiByZXNjdWVEYXRhLmxvY2F0aW9uSURcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gcmVzY3VlRGF0YS5sb2NhdGlvbklEXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGV2ZW50Q3VycmVudExvY2F0aW9uLmdlb0xvY2F0aW9uSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9wZXJhdGlvblR5cGU6IHJlc2N1ZURhdGEub3BlcmF0aW9uVHlwZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPy5tYXAoKHR5cGU6IGFueSkgPT4gdHlwZS52YWx1ZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5qb2luKCcsJyksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9wZXJhdGlvbkRlc2NyaXB0aW9uOiByZXNjdWVEYXRhLm9wZXJhdGlvbkRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWxUeXBlRGVzY3JpcHRpb246IHJlc2N1ZURhdGEudmVzc2VsVHlwZURlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgYXdhaXQgY2dFdmVudE1pc3Npb25Nb2RlbC5zYXZlKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IGdlbmVyYXRlVW5pcXVlSWQoKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWlzc2lvblR5cGU6IG1pc3Npb25EYXRhLm1pc3Npb25UeXBlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogbWlzc2lvbkRhdGEuZGVzY3JpcHRpb24sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9wZXJhdGlvbkRlc2NyaXB0aW9uOiBtaXNzaW9uRGF0YS5vcGVyYXRpb25EZXNjcmlwdGlvbixcclxuICAgICAgICAgICAgICAgICAgICAgICAgb3BlcmF0aW9uT3V0Y29tZTogbWlzc2lvbkRhdGEub3BlcmF0aW9uT3V0Y29tZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29tcGxldGVkQXQ6IHRpbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRMb2NhdGlvbklEOiByZXNjdWVEYXRhLmxvY2F0aW9uSURcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gcmVzY3VlRGF0YS5sb2NhdGlvbklEXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGV2ZW50Q3VycmVudExvY2F0aW9uLmdlb0xvY2F0aW9uSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGV2ZW50SUQ6ICtkYXRhPy5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgZXZlbnRUeXBlOiAnVmVzc2VsUmVzY3VlJyxcclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgIGhhbmRsZVNhdmVQYXJlbnQoK2RhdGE/LmlkLCAwKVxyXG4gICAgICAgICAgICAgICAgICAgIGNsb3NlTW9kYWwoKVxyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICBjcmVhdGVFdmVudFR5cGVfVmVzc2VsUmVzY3VlKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbnB1dDoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbE5hbWU6IHJlc2N1ZURhdGEudmVzc2VsTmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYWxsU2lnbjogcmVzY3VlRGF0YS5jYWxsU2lnbixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwb2I6ICtyZXNjdWVEYXRhLnBvYixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYXRpdHVkZTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzY3VlRGF0YS5sYXRpdHVkZSA+IDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gcmVzY3VlRGF0YS5sYXRpdHVkZT8udG9TdHJpbmcoKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBjdXJyZW50TG9jYXRpb24ubGF0aXR1ZGU/LnRvU3RyaW5nKCksXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvbmdpdHVkZTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzY3VlRGF0YS5sb25naXR1ZGUgPiAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHJlc2N1ZURhdGEubG9uZ2l0dWRlPy50b1N0cmluZygpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGN1cnJlbnRMb2NhdGlvbi5sb25naXR1ZGU/LnRvU3RyaW5nKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9jYXRpb25EZXNjcmlwdGlvbjpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzY3VlRGF0YS5sb2NhdGlvbkRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbExlbmd0aDogK3Jlc2N1ZURhdGEudmVzc2VsTGVuZ3RoLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbFR5cGU6IHJlc2N1ZURhdGEudmVzc2VsVHlwZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYWtlQW5kTW9kZWw6IHJlc2N1ZURhdGEubWFrZUFuZE1vZGVsLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiByZXNjdWVEYXRhLmNvbG9yLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG93bmVyTmFtZTogcmVzY3VlRGF0YS5vd25lck5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGhvbmU6IHJlc2N1ZURhdGEucGhvbmUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZW1haWw6IHJlc2N1ZURhdGEuZW1haWwsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWRkcmVzczogcmVzY3VlRGF0YS5hZGRyZXNzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG93bmVyT25Cb2FyZDogcmVzY3VlRGF0YS5vd25lck9uQm9hcmQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2dNZW1iZXJzaGlwVHlwZTogJ2NnbnonLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNnTWVtYmVyc2hpcDogcmVzY3VlRGF0YS5jZ01lbWJlcnNoaXAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWlzc2lvbklEOiByZXNjdWVEYXRhLm1pc3Npb25JRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWxMb2NhdGlvbklEOiByZXNjdWVEYXRhLmxvY2F0aW9uSURcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyByZXNjdWVEYXRhLmxvY2F0aW9uSURcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBldmVudEN1cnJlbnRMb2NhdGlvbi5nZW9Mb2NhdGlvbklELFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wZXJhdGlvblR5cGU6IHJlc2N1ZURhdGEub3BlcmF0aW9uVHlwZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/Lm1hcCgodHlwZTogYW55KSA9PiB0eXBlLnZhbHVlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuam9pbignLCcpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wZXJhdGlvbkRlc2NyaXB0aW9uOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXNjdWVEYXRhLm9wZXJhdGlvbkRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbFR5cGVEZXNjcmlwdGlvbjpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzY3VlRGF0YS52ZXNzZWxUeXBlRGVzY3JpcHRpb24sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIEV4cG9zZSB0aGUgc2F2ZSBmdW5jdGlvbiB0byBwYXJlbnQgY29tcG9uZW50IHZpYSByZWZcclxuICAgICAgICB1c2VJbXBlcmF0aXZlSGFuZGxlKFxyXG4gICAgICAgICAgICByZWYsXHJcbiAgICAgICAgICAgICgpID0+ICh7XHJcbiAgICAgICAgICAgICAgICBzYXZlOiBoYW5kbGVTYXZlLFxyXG4gICAgICAgICAgICB9KSxcclxuICAgICAgICAgICAgW2hhbmRsZVNhdmVdLFxyXG4gICAgICAgIClcclxuXHJcbiAgICAgICAgY29uc3QgW2NyZWF0ZUV2ZW50VHlwZV9WZXNzZWxSZXNjdWVdID0gdXNlTXV0YXRpb24oXHJcbiAgICAgICAgICAgIENyZWF0ZUV2ZW50VHlwZV9WZXNzZWxSZXNjdWUsXHJcbiAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2UpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuY3JlYXRlRXZlbnRUeXBlX1Zlc3NlbFJlc2N1ZVxyXG4gICAgICAgICAgICAgICAgICAgIGNyZWF0ZUNHRXZlbnRNaXNzaW9uKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbnB1dDoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pc3Npb25UeXBlOiBtaXNzaW9uRGF0YS5taXNzaW9uVHlwZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogbWlzc2lvbkRhdGEuZGVzY3JpcHRpb24sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3BlcmF0aW9uRGVzY3JpcHRpb246XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pc3Npb25EYXRhLm9wZXJhdGlvbkRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wZXJhdGlvbk91dGNvbWU6IG1pc3Npb25EYXRhLm9wZXJhdGlvbk91dGNvbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29tcGxldGVkQXQ6IHRpbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudExvY2F0aW9uSUQ6IHJlc2N1ZURhdGEubG9jYXRpb25JRFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHJlc2N1ZURhdGEubG9jYXRpb25JRFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGV2ZW50Q3VycmVudExvY2F0aW9uLmdlb0xvY2F0aW9uSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXZlbnRJRDogK2RhdGE/LmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV2ZW50VHlwZTogJ1Zlc3NlbFJlc2N1ZScsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgaGFuZGxlU2F2ZVBhcmVudCgrZGF0YT8uaWQsIDApXHJcbiAgICAgICAgICAgICAgICAgICAgY2xvc2VNb2RhbCgpXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgb25FcnJvcjogKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgdmVzc2VsIHJlc2N1ZScsIGVycm9yKVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICApXHJcblxyXG4gICAgICAgIGNvbnN0IFt1cGRhdGVFdmVudFR5cGVfVmVzc2VsUmVzY3VlXSA9IHVzZU11dGF0aW9uKFxyXG4gICAgICAgICAgICBVcGRhdGVFdmVudFR5cGVfVmVzc2VsUmVzY3VlLFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLnVwZGF0ZUV2ZW50VHlwZV9WZXNzZWxSZXNjdWVcclxuICAgICAgICAgICAgICAgICAgICBpZiAocmVzY3VlRGF0YS5taXNzaW9uSUQgPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZUNHRXZlbnRNaXNzaW9uKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkOiByZXNjdWVEYXRhLm1pc3Npb25JRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWlzc2lvblR5cGU6IG1pc3Npb25EYXRhLm1pc3Npb25UeXBlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogbWlzc2lvbkRhdGEuZGVzY3JpcHRpb24sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wZXJhdGlvbkRlc2NyaXB0aW9uOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWlzc2lvbkRhdGEub3BlcmF0aW9uRGVzY3JpcHRpb24sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wZXJhdGlvbk91dGNvbWU6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaXNzaW9uRGF0YS5vcGVyYXRpb25PdXRjb21lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb21wbGV0ZWRBdDogdGltZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudExvY2F0aW9uSUQ6IHJlc2N1ZURhdGEubG9jYXRpb25JRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXZlbnRJRDogK2RhdGE/LmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBldmVudFR5cGU6ICdWZXNzZWxSZXNjdWUnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYXQ6IGN1cnJlbnRNaXNzaW9uTG9jYXRpb24ubGF0aXR1ZGU/LnRvU3RyaW5nKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvbmc6IGN1cnJlbnRNaXNzaW9uTG9jYXRpb24ubG9uZ2l0dWRlPy50b1N0cmluZygpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNyZWF0ZUNHRXZlbnRNaXNzaW9uKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pc3Npb25UeXBlOiBtaXNzaW9uRGF0YS5taXNzaW9uVHlwZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246IG1pc3Npb25EYXRhLmRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcGVyYXRpb25EZXNjcmlwdGlvbjpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pc3Npb25EYXRhLm9wZXJhdGlvbkRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcGVyYXRpb25PdXRjb21lOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWlzc2lvbkRhdGEub3BlcmF0aW9uT3V0Y29tZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29tcGxldGVkQXQ6IHRpbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRMb2NhdGlvbklEOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzY3VlRGF0YS5jdXJyZW50TG9jYXRpb25JRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXZlbnRJRDogK2RhdGE/LmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBldmVudFR5cGU6ICdWZXNzZWxSZXNjdWUnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYXQ6IGN1cnJlbnRNaXNzaW9uTG9jYXRpb24ubGF0aXR1ZGUudG9TdHJpbmcoKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9uZzogY3VycmVudE1pc3Npb25Mb2NhdGlvbi5sb25naXR1ZGUudG9TdHJpbmcoKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgaGFuZGxlU2F2ZVBhcmVudCgrY3VycmVudFJlc2N1ZUlELCAwKVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIG9uRXJyb3I6IChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIHZlc3NlbCByZXNjdWUnLCBlcnJvcilcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgKVxyXG5cclxuICAgICAgICBjb25zdCBbY3JlYXRlQ0dFdmVudE1pc3Npb25dID0gdXNlTXV0YXRpb24oQ3JlYXRlQ0dFdmVudE1pc3Npb24sIHtcclxuICAgICAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZSkgPT4ge30sXHJcbiAgICAgICAgICAgIG9uRXJyb3I6IChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgQ0cgRXZlbnQgTWlzc2lvbicsIGVycm9yKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0pXHJcblxyXG4gICAgICAgIGNvbnN0IFt1cGRhdGVDR0V2ZW50TWlzc2lvbl0gPSB1c2VNdXRhdGlvbihVcGRhdGVDR0V2ZW50TWlzc2lvbiwge1xyXG4gICAgICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlKSA9PiB7fSxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBDRyBFdmVudCBNaXNzaW9uJywgZXJyb3IpXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSlcclxuXHJcbiAgICAgICAgY29uc3QgaGFuZGxlTG9jYXRpb25DaGFuZ2UgPSAodmFsdWU6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAvLyBJZiB2YWx1ZSBpcyBudWxsIG9yIHVuZGVmaW5lZCwgcmV0dXJuIGVhcmx5XHJcbiAgICAgICAgICAgIGlmICghdmFsdWUpIHJldHVyblxyXG5cclxuICAgICAgICAgICAgLy8gQ2hlY2sgaWYgdGhlIHZhbHVlIGlzIGZyb20gZHJvcGRvd24gc2VsZWN0aW9uIChoYXMgJ3ZhbHVlJyBwcm9wZXJ0eSlcclxuICAgICAgICAgICAgaWYgKHZhbHVlLnZhbHVlKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBIYW5kbGUgbG9jYXRpb24gc2VsZWN0ZWQgZnJvbSBkcm9wZG93blxyXG4gICAgICAgICAgICAgICAgc2V0UmVzY3VlRGF0YSgocHJldjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgICAgICAgICAgbG9jYXRpb25JRDogK3ZhbHVlLnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBsYXRpdHVkZTogbnVsbCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbG9uZ2l0dWRlOiBudWxsLFxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoXHJcbiAgICAgICAgICAgICAgICB2YWx1ZS5sYXRpdHVkZSAhPT0gdW5kZWZpbmVkICYmXHJcbiAgICAgICAgICAgICAgICB2YWx1ZS5sb25naXR1ZGUgIT09IHVuZGVmaW5lZFxyXG4gICAgICAgICAgICApIHtcclxuICAgICAgICAgICAgICAgIC8vIEhhbmRsZSBkaXJlY3QgY29vcmRpbmF0ZXMgaW5wdXRcclxuICAgICAgICAgICAgICAgIHNldFJlc2N1ZURhdGEoKHByZXY6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxvY2F0aW9uSUQ6IDAsIC8vIFJlc2V0IGdlb0xvY2F0aW9uSUQgd2hlbiB1c2luZyBkaXJlY3QgY29vcmRpbmF0ZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgbGF0aXR1ZGU6IHZhbHVlLmxhdGl0dWRlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBsb25naXR1ZGU6IHZhbHVlLmxvbmdpdHVkZSxcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCBoYW5kbGVNaXNzaW9uTG9jYXRpb25DaGFuZ2UgPSAodmFsdWU6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAvLyBJZiB2YWx1ZSBpcyBudWxsIG9yIHVuZGVmaW5lZCwgcmV0dXJuIGVhcmx5XHJcbiAgICAgICAgICAgIGlmICghdmFsdWUpIHJldHVyblxyXG5cclxuICAgICAgICAgICAgLy8gQ2hlY2sgaWYgdGhlIHZhbHVlIGlzIGZyb20gZHJvcGRvd24gc2VsZWN0aW9uIChoYXMgJ3ZhbHVlJyBwcm9wZXJ0eSlcclxuICAgICAgICAgICAgaWYgKHZhbHVlLnZhbHVlKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBIYW5kbGUgbG9jYXRpb24gc2VsZWN0ZWQgZnJvbSBkcm9wZG93blxyXG4gICAgICAgICAgICAgICAgc2V0TWlzc2lvbkRhdGEoe1xyXG4gICAgICAgICAgICAgICAgICAgIC4uLm1pc3Npb25EYXRhLFxyXG4gICAgICAgICAgICAgICAgICAgIGN1cnJlbnRMb2NhdGlvbklEOiArdmFsdWUudmFsdWUsXHJcbiAgICAgICAgICAgICAgICAgICAgbGF0OiBudWxsLFxyXG4gICAgICAgICAgICAgICAgICAgIGxvbmc6IG51bGwsXHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKFxyXG4gICAgICAgICAgICAgICAgdmFsdWUubGF0aXR1ZGUgIT09IHVuZGVmaW5lZCAmJlxyXG4gICAgICAgICAgICAgICAgdmFsdWUubG9uZ2l0dWRlICE9PSB1bmRlZmluZWRcclxuICAgICAgICAgICAgKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBIYW5kbGUgZGlyZWN0IGNvb3JkaW5hdGVzIGlucHV0XHJcbiAgICAgICAgICAgICAgICBzZXRNaXNzaW9uRGF0YSh7XHJcbiAgICAgICAgICAgICAgICAgICAgLi4ubWlzc2lvbkRhdGEsXHJcbiAgICAgICAgICAgICAgICAgICAgY3VycmVudExvY2F0aW9uSUQ6IDAsIC8vIFJlc2V0IGN1cnJlbnRMb2NhdGlvbklEIHdoZW4gdXNpbmcgZGlyZWN0IGNvb3JkaW5hdGVzXHJcbiAgICAgICAgICAgICAgICAgICAgbGF0OiB2YWx1ZS5sYXRpdHVkZSxcclxuICAgICAgICAgICAgICAgICAgICBsb25nOiB2YWx1ZS5sb25naXR1ZGUsXHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCBoYW5kbGVDcmVhdGVDb21tZW50ID0gKCkgPT4ge1xyXG4gICAgICAgICAgICBpZiAoc2VsZWN0ZWRFdmVudCkge1xyXG4gICAgICAgICAgICAgICAgc2V0T3BlbkNvbW1lbnRzRGlhbG9nKHRydWUpXHJcbiAgICAgICAgICAgICAgICBoYW5kbGVFZGl0b3JDaGFuZ2UoJycpXHJcbiAgICAgICAgICAgICAgICBzZXRDb21tZW50RGF0YShmYWxzZSlcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHRvYXN0LmVycm9yKFxyXG4gICAgICAgICAgICAgICAgICAgICdQbGVhc2Ugc2F2ZSB0aGUgZXZlbnQgZmlyc3QgaW4gb3JkZXIgdG8gY3JlYXRlIHRpbWVsaW5lIScsXHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IGhhbmRsZUVkaXRDb21tZW50ID0gKGNvbW1lbnQ6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBzZXRPcGVuQ29tbWVudHNEaWFsb2codHJ1ZSlcclxuICAgICAgICAgICAgc2V0Q29tbWVudERhdGEoY29tbWVudClcclxuICAgICAgICAgICAgaGFuZGxlRWRpdG9yQ2hhbmdlKGNvbW1lbnQuZGVzY3JpcHRpb24pXHJcbiAgICAgICAgICAgIHNldENvbW1lbnRUaW1lKGRheWpzKGNvbW1lbnQudGltZSkuZm9ybWF0KCdISDptbScpKVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgaGFuZGxlRGVsZXRlQ29tbWVudCA9IChjb21tZW50SWQ6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBjb21tZW50ID0gdGltZWxpbmU/LmZpbmQoKGM6IGFueSkgPT4gYy5pZCA9PT0gY29tbWVudElkKVxyXG4gICAgICAgICAgICBpZiAoY29tbWVudCkge1xyXG4gICAgICAgICAgICAgICAgc2V0RGVsZXRlQ29tbWVudHNEaWFsb2codHJ1ZSlcclxuICAgICAgICAgICAgICAgIHNldENvbW1lbnREYXRhKGNvbW1lbnQpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IGhhbmRsZURlbGV0ZUNvbW1lbnRzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgICBpZiAob2ZmbGluZSkge1xyXG4gICAgICAgICAgICAgICAgYXdhaXQgbWlzc2lvblRpbWVsaW5lTW9kZWwuc2F2ZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgaWQ6IGNvbW1lbnREYXRhPy5pZCxcclxuICAgICAgICAgICAgICAgICAgICBhcmNoaXZlZDogdHJ1ZSxcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICBzZXRPcGVuQ29tbWVudHNEaWFsb2coZmFsc2UpXHJcbiAgICAgICAgICAgICAgICBzZXREZWxldGVDb21tZW50c0RpYWxvZyhmYWxzZSlcclxuICAgICAgICAgICAgICAgIGdldEN1cnJlbnRFdmVudChjdXJyZW50UmVzY3VlSUQpXHJcbiAgICAgICAgICAgICAgICBzZXREZWxldGVDb21tZW50c0RpYWxvZyhmYWxzZSlcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHVwZGF0ZU1pc3Npb25UaW1lbGluZSh7XHJcbiAgICAgICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogY29tbWVudERhdGE/LmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYXJjaGl2ZWQ6IHRydWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICBzZXREZWxldGVDb21tZW50c0RpYWxvZyhmYWxzZSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCBvZmZsaW5lR2V0U2VhTG9nc01lbWJlcnNMaXN0ID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgICAvLyBnZXRTZWFMb2dzTWVtYmVyc0xpc3QoaGFuZGxlU2V0TWVtYmVyTGlzdClcclxuICAgICAgICAgICAgY29uc3QgbWVtYmVycyA9IGF3YWl0IG1lbWJlck1vZGVsLmdldEFsbCgpXHJcbiAgICAgICAgICAgIGhhbmRsZVNldE1lbWJlckxpc3QobWVtYmVycylcclxuICAgICAgICB9XHJcbiAgICAgICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICAgICAgaWYgKG9mZmxpbmUpIHtcclxuICAgICAgICAgICAgICAgIG9mZmxpbmVHZXRTZWFMb2dzTWVtYmVyc0xpc3QoKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSwgW29mZmxpbmVdKVxyXG5cclxuICAgICAgICBnZXRWZXNzZWxCeUlEKCt2ZXNzZWxJRCwgc2V0VmVzc2VsLCBvZmZsaW5lKVxyXG5cclxuICAgICAgICBjb25zdCBbZ2V0U2VjdGlvbkNyZXdNZW1iZXJzX0xvZ0Jvb2tFbnRyeVNlY3Rpb25dID0gdXNlTGF6eVF1ZXJ5KFxyXG4gICAgICAgICAgICBDcmV3TWVtYmVyc19Mb2dCb29rRW50cnlTZWN0aW9uLFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICBmZXRjaFBvbGljeTogJ2NhY2hlLWFuZC1uZXR3b3JrJyxcclxuICAgICAgICAgICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2U6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGxldCBkYXRhID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzcG9uc2UucmVhZENyZXdNZW1iZXJzX0xvZ0Jvb2tFbnRyeVNlY3Rpb25zLm5vZGVzXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3Jld01lbWJlcnMoZGF0YSlcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICdDcmV3TWVtYmVyc19Mb2dCb29rRW50cnlTZWN0aW9uIGVycm9yJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3IsXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICApXHJcblxyXG4gICAgICAgIGNvbnN0IGhhbmRsZVNldExvZ2Jvb2sgPSBhc3luYyAobG9nYm9vazogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIHNldExvZ2Jvb2sobG9nYm9vaylcclxuICAgICAgICAgICAgY29uc3Qgc2VjdGlvblR5cGVzID0gQXJyYXkuZnJvbShcclxuICAgICAgICAgICAgICAgIG5ldyBTZXQoXHJcbiAgICAgICAgICAgICAgICAgICAgbG9nYm9vay5sb2dCb29rRW50cnlTZWN0aW9ucy5ub2Rlcy5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIChzZWM6IGFueSkgPT4gc2VjLmNsYXNzTmFtZSxcclxuICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgKS5tYXAoKHR5cGUpID0+ICh7XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU6IHR5cGUsXHJcbiAgICAgICAgICAgICAgICBpZHM6IGxvZ2Jvb2subG9nQm9va0VudHJ5U2VjdGlvbnMubm9kZXNcclxuICAgICAgICAgICAgICAgICAgICAuZmlsdGVyKChzZWM6IGFueSkgPT4gc2VjLmNsYXNzTmFtZSA9PT0gdHlwZSlcclxuICAgICAgICAgICAgICAgICAgICAubWFwKChzZWM6IGFueSkgPT4gc2VjLmlkKSxcclxuICAgICAgICAgICAgfSkpXHJcbiAgICAgICAgICAgIHNlY3Rpb25UeXBlcy5mb3JFYWNoKGFzeW5jIChzZWN0aW9uOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGlmIChcclxuICAgICAgICAgICAgICAgICAgICBzZWN0aW9uLmNsYXNzTmFtZSA9PT1cclxuICAgICAgICAgICAgICAgICAgICAnU2VhTG9nc1xcXFxDcmV3TWVtYmVyc19Mb2dCb29rRW50cnlTZWN0aW9uJ1xyXG4gICAgICAgICAgICAgICAgKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKG9mZmxpbmUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGNtbGJzTW9kZWwuZ2V0QnlJZHMoc2VjdGlvbi5pZHMpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldENyZXdNZW1iZXJzKGRhdGEpXHJcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgc2VhcmNoRmlsdGVyOiBTZWFyY2hGaWx0ZXIgPSB7fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZWFyY2hGaWx0ZXIuaWQgPSB7IGluOiBzZWN0aW9uLmlkcyB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGdldFNlY3Rpb25DcmV3TWVtYmVyc19Mb2dCb29rRW50cnlTZWN0aW9uKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbHRlcjogc2VhcmNoRmlsdGVyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCBnZXRMb2dCb29rRW50cnlCeUlEID0gYXN5bmMgKGlkOiBudW1iZXIpID0+IHtcclxuICAgICAgICAgICAgaWYgKG9mZmxpbmUpIHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBsb2dib29rTW9kZWwuZ2V0QnlJZChpZClcclxuICAgICAgICAgICAgICAgIGlmIChkYXRhKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgaGFuZGxlU2V0TG9nYm9vayhkYXRhKVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgcXVlcnlMb2dCb29rRW50cnkoe1xyXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBsb2dib29rRW50cnlJZDogK2lkLFxyXG4gICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCBbcXVlcnlMb2dCb29rRW50cnldID0gdXNlTGF6eVF1ZXJ5KEdFVF9MT0dCT09LX0VOVFJZX0JZX0lELCB7XHJcbiAgICAgICAgICAgIGZldGNoUG9saWN5OiAnY2FjaGUtYW5kLW5ldHdvcmsnLFxyXG4gICAgICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5yZWFkT25lTG9nQm9va0VudHJ5XHJcbiAgICAgICAgICAgICAgICBpZiAoZGF0YSkge1xyXG4gICAgICAgICAgICAgICAgICAgIGhhbmRsZVNldExvZ2Jvb2soZGF0YSlcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3F1ZXJ5TG9nQm9va0VudHJ5IGVycm9yJywgZXJyb3IpXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSlcclxuXHJcbiAgICAgICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICAgICAgZ2V0TG9nQm9va0VudHJ5QnlJRCgrbG9nZW50cnlJRClcclxuICAgICAgICB9LCBbXSlcclxuXHJcbiAgICAgICAgY29uc3QgW3F1ZXJ5VmVzc2VsQ3Jld3NdID0gdXNlTGF6eVF1ZXJ5KENSRVdfTElTVCwge1xyXG4gICAgICAgICAgICBmZXRjaFBvbGljeTogJ2NhY2hlLWFuZC1uZXR3b3JrJyxcclxuICAgICAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLnJlYWRTZWFMb2dzTWVtYmVyc1xyXG4gICAgICAgICAgICAgICAgaWYgKGRhdGEpIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBhbGxNZW1iZXJzID0gZGF0YS5ub2Rlc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAuZmlsdGVyKChpdGVtOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAraXRlbS5pZCAhPT0gK21hc3RlcklEXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5tYXAoKG1lbWJlcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBjb25zdCBjcmV3V2l0aFRyYWluaW5nID0gR2V0Q3Jld0xpc3RXaXRoVHJhaW5pbmdTdGF0dXMoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyAgICAgW21lbWJlcl0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyAgICAgW3Zlc3NlbF0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyApWzBdXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiBgJHttZW1iZXIuZmlyc3ROYW1lIHx8ICcnfSAke21lbWJlci5zdXJuYW1lIHx8ICcnfWAudHJpbSgpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBtZW1iZXIuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gZGF0YTogY3Jld1dpdGhUcmFpbmluZyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcm9maWxlOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpcnN0TmFtZTogbWVtYmVyLmZpcnN0TmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3VybmFtZTogbWVtYmVyLnN1cm5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF2YXRhcjogbWVtYmVyLnByb2ZpbGVJbWFnZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG5cclxuICAgICAgICAgICAgICAgICAgICBzZXRBbGxNZW1iZXJzKGFsbE1lbWJlcnMpXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG1lbWJlcnMgPSBhbGxNZW1iZXJzLmZpbHRlcigobWVtYmVyOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFjcmV3TWVtYmVycykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgIUFycmF5LmlzQXJyYXkoY3Jld01lbWJlcnMpIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAhY3Jld01lbWJlcnMuc29tZShcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoc2VjdGlvbjogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWN0aW9uICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlY3Rpb24uY3Jld01lbWJlciAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWN0aW9uLmNyZXdNZW1iZXIuaWQgPT09IG1lbWJlci52YWx1ZSAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWN0aW9uLnB1bmNoT3V0ID09PSBudWxsLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgfSlcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbWVtYmVyT3B0aW9ucyA9IG1lbWJlcnMuZmlsdGVyKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAobWVtYmVyOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAhY3Jld01lbWJlcnNMaXN0IHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAhQXJyYXkuaXNBcnJheShjcmV3TWVtYmVyc0xpc3QpIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAhY3Jld01lbWJlcnNMaXN0LmluY2x1ZGVzKCttZW1iZXIudmFsdWUpLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3Jld01lbWJlck9wdGlvbnMobWVtYmVyT3B0aW9ucylcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3F1ZXJ5VmVzc2VsQ3Jld3MgZXJyb3InLCBlcnJvcilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9KVxyXG5cclxuICAgICAgICBjb25zdCBsb2FkVmVzc2VsQ3Jld3MgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICAgIGlmIChvZmZsaW5lKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgc2VhTG9nc01lbWJlck1vZGVsLmdldEJ5VmVzc2VsSWQodmVzc2VsSUQpXHJcbiAgICAgICAgICAgICAgICBzZXRBbGxWZXNzZWxDcmV3cyhkYXRhKVxyXG4gICAgICAgICAgICAgICAgaWYgKGRhdGEpIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBtZW1iZXJzID0gZGF0YVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuZmlsdGVyKChpdGVtOiBhbnkpID0+ICtpdGVtLmlkICE9PSArbG9nYm9vay5tYXN0ZXIuaWQpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5tYXAoKG1lbWJlcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjcmV3V2l0aFRyYWluaW5nID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBHZXRDcmV3TGlzdFdpdGhUcmFpbmluZ1N0YXR1cyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW21lbWJlcl0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFt2ZXNzZWxdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClbMF1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IGAke21lbWJlci5maXJzdE5hbWUgfHwgJyd9ICR7bWVtYmVyLnN1cm5hbWUgfHwgJyd9YC50cmltKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IG1lbWJlci5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiBjcmV3V2l0aFRyYWluaW5nLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByb2ZpbGU6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlyc3ROYW1lOiBtZW1iZXIuZmlyc3ROYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdXJuYW1lOiBtZW1iZXIuc3VybmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXZhdGFyOiBtZW1iZXIucHJvZmlsZUltYWdlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3Jld01lbWJlck9wdGlvbnMobWVtYmVycylcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGF3YWl0IHF1ZXJ5VmVzc2VsQ3Jld3Moe1xyXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBmaWx0ZXI6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlaGljbGVzOiB7IGlkOiB7IGVxOiB2ZXNzZWxJRCB9IH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc0FyY2hpdmVkOiB7IGVxOiBmYWxzZSB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgICAgICBpZiAoaXNMb2FkaW5nKSB7XHJcbiAgICAgICAgICAgICAgICBsb2FkVmVzc2VsQ3Jld3MoKVxyXG4gICAgICAgICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSwgW2lzTG9hZGluZ10pXHJcblxyXG4gICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtsb2NrZWQgPyAncG9pbnRlci1ldmVudHMtbm9uZScgOiAnJ30gcHQtMGB9PlxyXG4gICAgICAgICAgICAgICAge3R5cGUgPT09ICdUYXNraW5nQ29tcGxldGUnICYmIChcclxuICAgICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTMgZ2FwLTYgcGItMCBwdC0wIHB4LTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXktMCAgY29sLXNwYW4tMyBtZDpjb2wtc3Bhbi0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEgzPk1pc3Npb24gY29tcGxldGU8L0gzPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBSZWNvcmQgdGhlIG9wZXJhdGlvbiBvdXRjb21lLCBsb2NhdGlvbiBhbmRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGltZSBvZiBjb21wbGV0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9QPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTMgbWQ6Y29sLXNwYW4tMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXktMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q29tYm9ib3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e29wZXJhdGlvbk91dGNvbWVzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e29wZXJhdGlvbk91dGNvbWVzPy5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChvdXRjb21lOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG91dGNvbWUudmFsdWUgPT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWlzc2lvbkRhdGE/Lm9wZXJhdGlvbk91dGNvbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyh2YWx1ZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0TWlzc2lvbkRhdGEoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5taXNzaW9uRGF0YSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3BlcmF0aW9uT3V0Y29tZTogdmFsdWU/LnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJPcGVyYXRpb24gb3V0Y29tZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge21pc3Npb25EYXRhPy5vcGVyYXRpb25PdXRjb21lID09ICdPdGhlcicgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm15LTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0YXJlYVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPXtgb3BlcmF0aW9uLW91dGNvbWUtZGVzY3JpcHRpb25gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvd3M9ezR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXsnJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkRlc2NyaXB0aW9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pc3Npb25EYXRhPy5vcGVyYXRpb25EZXNjcmlwdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBtaXNzaW9uRGF0YT8ub3BlcmF0aW9uRGVzY3JpcHRpb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJydcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0TWlzc2lvbkRhdGEoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ubWlzc2lvbkRhdGEsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcGVyYXRpb25EZXNjcmlwdGlvbjogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnb3BlcmF0aW9uLW91dGNvbWUtZGVzY3JpcHRpb24nLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgYXMgSFRNTElucHV0RWxlbWVudFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKS52YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ2FwLTYgcGItNCBwdC0wXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteS00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yLjVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0aW1lbGluZSAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpbWVsaW5lPy5tYXAoKGNvbW1lbnQ6IGFueSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UmVjb3JkQ2FyZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtgJHtjb21tZW50LmlkfS1yZWNvcmQtJHtjb21tZW50LnRpbWUgfHwgJyd9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlY29yZD17Y29tbWVudH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uRWRpdD17aGFuZGxlRWRpdENvbW1lbnR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkRlbGV0ZT17aGFuZGxlRGVsZXRlQ29tbWVudH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBnYXAtM1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNob3dJbnB1dERldGFpbHNQYW5lbCh0cnVlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBWZXNzZWwgZGV0YWlsc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXBUcmlnZ2VyIGFzQ2hpbGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ3JlYXRlQ29tbWVudH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IXNlbGVjdGVkRXZlbnR9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEFkZCBub3Rlcy9jb21tZW50c1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcFRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwQ29udGVudFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGlkZGVuPXtzZWxlY3RlZEV2ZW50Py5pZCA+IDB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgUGxlYXNlIHNhdmUgdGhlIGV2ZW50IGZpcnN0IGluIG9yZGVyIHRvXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcmVhdGUgdGltZWxpbmUhXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICB7c2hvd1NhdmVCdXR0b25zICYmIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImJhY2tcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbkxlZnQ9e0Fycm93TGVmdH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGNsb3NlTW9kYWwoKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBDYW5jZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17aGFuZGxlU2F2ZX0+U2F2ZTwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIDxBbGVydERpYWxvZ05ld1xyXG4gICAgICAgICAgICAgICAgICAgIG9wZW5EaWFsb2c9e29wZW5Db21tZW50c0RpYWxvZ31cclxuICAgICAgICAgICAgICAgICAgICBzZXRPcGVuRGlhbG9nPXtzZXRPcGVuQ29tbWVudHNEaWFsb2d9XHJcbiAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInhsXCJcclxuICAgICAgICAgICAgICAgICAgICBoYW5kbGVDcmVhdGU9e2hhbmRsZVNhdmVDb21tZW50c31cclxuICAgICAgICAgICAgICAgICAgICB0aXRsZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbW1lbnREYXRhPy5pZCA+IDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ1VwZGF0ZSBDb21tZW50J1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnQ3JlYXRlIENvbW1lbnQnXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIGFjdGlvblRleHQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb21tZW50RGF0YT8uaWQgPiAwID8gJ1VwZGF0ZScgOiAnQ3JlYXRlIENvbW1lbnQnXHJcbiAgICAgICAgICAgICAgICAgICAgfT5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJjb21tZW50LXR5cGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJDb21tZW50IFR5cGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPENvbWJvYm94XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJjb21tZW50LXR5cGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e2NvbW1lbnRUeXBlc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29tbWVudFR5cGVzPy5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAodHlwZTogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZS52YWx1ZSA9PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29tbWVudERhdGE/LmNvbW1lbnRUeXBlPy5yZXBsYWNlQWxsKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdfJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnICcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyh2YWx1ZTogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRDb21tZW50RGF0YSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5jb21tZW50RGF0YSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbW1lbnRUeXBlOiB2YWx1ZT8udmFsdWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VsZWN0IGNvbW1lbnQgdHlwZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJjb21tZW50X3RpbWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgVGltZSBvZiBDb21wbGV0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRpbWVGaWVsZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpbWU9e2NvbW1lbnRUaW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVRpbWVDaGFuZ2U9eyhkYXRlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlQ29tbWVudFRpbWVDaGFuZ2UoZGF0ZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpbWVJRD1cImNvbW1lbnRfdGltZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmllbGROYW1lPVwiY29tbWVudF90aW1lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiY29tbWVudFwiPkNvbW1lbnQgQ29udGVudDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RWRpdG9yXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJjb21tZW50XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIldyaXRlIHlvdXIgY29tbWVudCBoZXJlLi4uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgbWluLWgtWzE1MHB4XSBiZy1zZWNvbmRhcnktZm9yZWdyb3VuZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGVudD17Y29udGVudH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVFZGl0b3JDaGFuZ2U9e2hhbmRsZUVkaXRvckNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJhdXRob3JcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQXV0aG9yXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge21lbWJlcnMgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDb21ib2JveFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cImF1dGhvclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e2NyZXdNZW1iZXJPcHRpb25zfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y3Jld01lbWJlck9wdGlvbnM/LmZpbmQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAobWVtYmVyOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyLnZhbHVlID09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29tbWVudERhdGE/LmF1dGhvcj8uaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsodmFsdWU6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldENvbW1lbnREYXRhKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5jb21tZW50RGF0YSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdXRob3JJRDogdmFsdWU/LnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlbGVjdCBjcmV3XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L0FsZXJ0RGlhbG9nTmV3PlxyXG4gICAgICAgICAgICAgICAgPEFsZXJ0RGlhbG9nTmV3XHJcbiAgICAgICAgICAgICAgICAgICAgb3BlbkRpYWxvZz17ZGVsZXRlQ29tbWVudHNEaWFsb2d9XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0T3BlbkRpYWxvZz17c2V0RGVsZXRlQ29tbWVudHNEaWFsb2d9XHJcbiAgICAgICAgICAgICAgICAgICAgaGFuZGxlQ3JlYXRlPXtoYW5kbGVEZWxldGVDb21tZW50c31cclxuICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkRlbGV0ZSBDb21tZW50XCJcclxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwid2FybmluZ1wiXHJcbiAgICAgICAgICAgICAgICAgICAgYWN0aW9uVGV4dD1cIkNvbmZpcm0gZGVsZXRlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGRlbGV0ZSB0aGlzIGNvbW1lbnQ/IFRoaXMgYWN0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgY2Fubm90IGJlIHVuZG9uZSBhbmQgYWxsIGFzc29jaWF0ZWQgZGF0YSB3aWxsIGJlIHBlcm1hbmVudGx5XHJcbiAgICAgICAgICAgICAgICAgICAgcmVtb3ZlZCBmcm9tIHRoZSBzeXN0ZW0uXHJcbiAgICAgICAgICAgICAgICA8L0FsZXJ0RGlhbG9nTmV3PlxyXG4gICAgICAgICAgICAgICAgPFNoZWV0XHJcbiAgICAgICAgICAgICAgICAgICAgb3Blbj17c2hvd0lucHV0RGV0YWlsc1B9XHJcbiAgICAgICAgICAgICAgICAgICAgb25PcGVuQ2hhbmdlPXsob3BlbikgPT4gc2V0U2hvd0lucHV0RGV0YWlsc1BhbmVsKG9wZW4pfT5cclxuICAgICAgICAgICAgICAgICAgICA8U2hlZXRDb250ZW50XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpZGU9XCJyaWdodFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBtYXgtdy1tZCBzbTptYXgtdy14bCBiZy1iYWNrZ3JvdW5kIHBoYWJsZXQ6YmctbXV0ZWRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFNoZWV0SGVhZGVyPjwvU2hlZXRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBoLWZ1bGwgbWluLWgtWzQwMHB4XSBvdmVyZmxvdy1hdXRvXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2Nyb2xsQXJlYT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtZ3JvdyBzcGFjZS15LTYgcHktNCBteC0zXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBWZXNzZWwgRGV0YWlscyBTZWN0aW9uICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInBiLTNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXktNCB0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdXBwZXJjYXNlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBUYXJnZXQgVmVzc2VsIERldGFpbHNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFJlY29yZCB2ZXNzZWwgbmFtZSwgY2FsbHNpZ24gYW5kXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG51bWJlciBvZiBwZW9wbGUgb24gYm9hcmRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJ2ZXNzZWwtbmFtZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgVmVzc2VsIE5hbWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cInZlc3NlbC1uYW1lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgdmVzc2VsIG5hbWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlc2N1ZURhdGE/LnZlc3NlbE5hbWUgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0UmVzY3VlRGF0YSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnJlc2N1ZURhdGEsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbE5hbWU6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnRhcmdldC52YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJjYWxsLXNpZ25cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIENhbGwgU2lnblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY2FsbC1zaWduXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgY2FsbCBzaWduXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXNjdWVEYXRhPy5jYWxsU2lnbiB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICcnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRSZXNjdWVEYXRhKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucmVzY3VlRGF0YSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FsbFNpZ246XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnRhcmdldC52YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJwb2JcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFBlb3BsZSBPbiBCb2FyZCAoUE9CKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwicG9iXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBudW1iZXIgb2YgcGVvcGxlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbj17MX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtyZXNjdWVEYXRhPy5wb2IgfHwgMH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFJlc2N1ZURhdGEoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5yZXNjdWVEYXRhLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwb2I6IGUudGFyZ2V0LnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIFZlc3NlbCBEZXNjcmlwdGlvbiBTZWN0aW9uICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInBiLTNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXktNCB0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdXBwZXJjYXNlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBWZXNzZWwgRGVzY3JpcHRpb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEluY2x1ZGUgZGV0YWlscyBvZiB2ZXNzZWwgdHlwZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFrZSBhbmQgZGVzY3JpcHRvcnNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJ2ZXNzZWwtbGVuZ3RoXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBOdW1iZXIgb2YgVmVzc2Vsc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwidmVzc2VsLWxlbmd0aFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgdmVzc2VsIGxlbmd0aFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaW49ezF9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzY3VlRGF0YT8udmVzc2VsTGVuZ3RoIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgMFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0UmVzY3VlRGF0YSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnJlc2N1ZURhdGEsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbExlbmd0aDpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInZlc3NlbC10eXBlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBWZXNzZWwgdHlwZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q29tYm9ib3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e3Zlc3NlbFR5cGVzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Zlc3NlbFR5cGVzPy5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICh0eXBlOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGUudmFsdWUgPT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzY3VlRGF0YT8udmVzc2VsVHlwZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbHVlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRSZXNjdWVEYXRhKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucmVzY3VlRGF0YSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsVHlwZTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPy52YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VsZWN0IHZlc3NlbCB0eXBlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Jlc2N1ZURhdGE/LnZlc3NlbFR5cGUgPT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ090aGVyJyAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInZlc3NlbC10eXBlLWRlc2NyaXB0aW9uXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgVmVzc2VsIHR5cGUgZGVzY3JpcHRpb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dGFyZWFcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cInZlc3NlbC10eXBlLWRlc2NyaXB0aW9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3dzPXszfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRGVzY3JpYmUgdGhlIHZlc3NlbCB0eXBlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlc2N1ZURhdGE/LnZlc3NlbFR5cGVEZXNjcmlwdGlvbiB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0UmVzY3VlRGF0YSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5yZXNjdWVEYXRhLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsVHlwZURlc2NyaXB0aW9uOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC52YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJtYWtlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBNYWtlIGFuZCBvZGVsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJtYWtlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgbWFrZSBhbmQgbW9kZWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlc2N1ZURhdGE/Lm1ha2VBbmRNb2RlbCB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICcnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRSZXNjdWVEYXRhKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucmVzY3VlRGF0YSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFrZUFuZE1vZGVsOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZS50YXJnZXQudmFsdWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiY29sb3JcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIENvbG9yXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJjb2xvclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHZlc3NlbCBjb2xvclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzY3VlRGF0YT8uY29sb3IgfHwgJydcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFJlc2N1ZURhdGEoe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5yZXNjdWVEYXRhLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogZS50YXJnZXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC52YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBPd25lcidzIERldGFpbHMgU2VjdGlvbiAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcmQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0zXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm15LTQgdGV4dC1zbSBmb250LXNlbWlib2xkIHVwcGVyY2FzZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgT3duZXIncyBEZXRhaWxzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBSZWNvcmQgdmVzc2VsIG93bmVyJ3MgZGV0YWlsc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbmQgbWVtYmVyc2hpcCBudW1iZXIgaWZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXBwbGljYWJsZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvUD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgc206Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwib3duZXItbmFtZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE93bmVyJ3MgTmFtZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwib3duZXItbmFtZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgb3duZXIncyBuYW1lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlc2N1ZURhdGE/Lm93bmVyTmFtZSB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0UmVzY3VlRGF0YSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5yZXNjdWVEYXRhLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3duZXJOYW1lOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC52YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJvd25lci1waG9uZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFBob25lIE51bWJlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwib3duZXItcGhvbmVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHBob25lIG51bWJlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXNjdWVEYXRhPy5waG9uZSB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0UmVzY3VlRGF0YSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5yZXNjdWVEYXRhLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGhvbmU6IGUudGFyZ2V0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIHNtOmdyaWQtY29scy0yIGdhcC00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImNnbnpcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBDb2FzdGd1YXJkIE5aIE1lbWJlcnNoaXBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cImNnbnpcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIG1lbWJlcnNoaXAgbnVtYmVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlc2N1ZURhdGE/LmNnTWVtYmVyc2hpcCB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0UmVzY3VlRGF0YSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5yZXNjdWVEYXRhLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2dNZW1iZXJzaGlwOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC52YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJvd25lci1lbWFpbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEVtYWlsIEFkZHJlc3NcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cIm93bmVyLWVtYWlsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBlbWFpbCBhZGRyZXNzXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlc2N1ZURhdGE/LmVtYWlsIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICcnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRSZXNjdWVEYXRhKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnJlc2N1ZURhdGEsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbWFpbDogZS50YXJnZXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAudmFsdWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cIm93bmVyLWFkZHJlc3NcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE93bmVyJ3MgQWRkcmVzc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dGFyZWFcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwib3duZXItYWRkcmVzc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3dzPXszfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBvd25lcidzIGFkZHJlc3NcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlc2N1ZURhdGE/LmFkZHJlc3MgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0UmVzY3VlRGF0YSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnJlc2N1ZURhdGEsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFkZHJlc3M6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnRhcmdldC52YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcHQtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJvd25lci1vbmJvYXJkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiSXMgdGhlIG93bmVyIG9uLWJvYXJkP1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZWZ0Q29udGVudD17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrYm94XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwib3duZXItb25ib2FyZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzY3VlRGF0YT8ub3duZXJPbkJvYXJkIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmYWxzZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJsZ1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzUmFkaW9TdHlsZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9eyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0UmVzY3VlRGF0YSh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucmVzY3VlRGF0YSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvd25lck9uQm9hcmQ6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQgPT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRydWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Njcm9sbEFyZWE+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIHAtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiB7QnV0dG9uID8gKCAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTaG93SW5wdXREZXRhaWxzUGFuZWwoZmFsc2UpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFNhdmUgQ2hhbmdlc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNob3dJbnB1dERldGFpbHNQYW5lbChmYWxzZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrIGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFNhdmUgQ2hhbmdlc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX0gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9TaGVldENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICA8L1NoZWV0PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApXHJcbiAgICB9LFxyXG4pXHJcblxyXG5WZXNzZWxSZXNjdWVGaWVsZHMuZGlzcGxheU5hbWUgPSAnVmVzc2VsUmVzY3VlRmllbGRzJ1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgVmVzc2VsUmVzY3VlRmllbGRzXHJcbiJdLCJuYW1lcyI6WyJkYXlqcyIsIlJlYWN0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJmb3J3YXJkUmVmIiwidXNlSW1wZXJhdGl2ZUhhbmRsZSIsInVzZVNlYXJjaFBhcmFtcyIsIkNyZWF0ZUV2ZW50VHlwZV9WZXNzZWxSZXNjdWUiLCJVcGRhdGVFdmVudFR5cGVfVmVzc2VsUmVzY3VlIiwiQ3JlYXRlQ0dFdmVudE1pc3Npb24iLCJVcGRhdGVDR0V2ZW50TWlzc2lvbiIsIkNyZWF0ZU1pc3Npb25UaW1lbGluZSIsIlVwZGF0ZU1pc3Npb25UaW1lbGluZSIsIkNSRVdfTElTVCIsIkNyZXdNZW1iZXJzX0xvZ0Jvb2tFbnRyeVNlY3Rpb24iLCJHRVRfTE9HQk9PS19FTlRSWV9CWV9JRCIsIkdldFRyaXBFdmVudF9WZXNzZWxSZXNjdWUiLCJnZXRTZWFMb2dzTWVtYmVyc0xpc3QiLCJnZXRWZXNzZWxCeUlEIiwiRWRpdG9yIiwidXNlTGF6eVF1ZXJ5IiwidXNlTXV0YXRpb24iLCJUaW1lRmllbGQiLCJTZWFMb2dzTWVtYmVyTW9kZWwiLCJFdmVudFR5cGVfVmVzc2VsUmVzY3VlTW9kZWwiLCJDR0V2ZW50TWlzc2lvbk1vZGVsIiwiTWlzc2lvblRpbWVsaW5lTW9kZWwiLCJnZW5lcmF0ZVVuaXF1ZUlkIiwidXNlTWVkaWFRdWVyeSIsIklucHV0IiwiTGFiZWwiLCJDb21ib2JveCIsIlRleHRhcmVhIiwiQWxlcnREaWFsb2dOZXciLCJBcnJvd0xlZnQiLCJ0b2FzdCIsIlNoZWV0IiwiU2hlZXRDb250ZW50IiwiU2hlZXRIZWFkZXIiLCJIMyIsIlAiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkSGVhZGVyIiwiQ2hlY2tib3giLCJHZXRDcmV3TGlzdFdpdGhUcmFpbmluZ1N0YXR1cyIsIkNyZXdNZW1iZXJzX0xvZ0Jvb2tFbnRyeVNlY3Rpb25Nb2RlbCIsIkxvZ0Jvb2tFbnRyeU1vZGVsIiwiQnV0dG9uIiwiU2Nyb2xsQXJlYSIsIlRvb2x0aXAiLCJUb29sdGlwQ29udGVudCIsIlRvb2x0aXBUcmlnZ2VyIiwiUmVjb3JkQ2FyZCIsIlZlc3NlbFJlc2N1ZUZpZWxkcyIsInJlZiIsImdlb0xvY2F0aW9ucyIsInNlbGVjdGVkRXZlbnQiLCJjbG9zZU1vZGFsIiwiaGFuZGxlU2F2ZVBhcmVudCIsImN1cnJlbnRSZXNjdWVJRCIsInR5cGUiLCJldmVudEN1cnJlbnRMb2NhdGlvbiIsImxvY2F0aW9uRGVzY3JpcHRpb24iLCJzZXRMb2NhdGlvbkRlc2NyaXB0aW9uIiwib2ZmbGluZSIsImxvY2tlZCIsInNob3dTYXZlQnV0dG9ucyIsInNlYXJjaFBhcmFtcyIsImxvY2F0aW9ucyIsInNldExvY2F0aW9ucyIsInRpbWUiLCJzZXRUaW1lIiwiZm9ybWF0Iiwib3BlbkNvbW1lbnRzRGlhbG9nIiwic2V0T3BlbkNvbW1lbnRzRGlhbG9nIiwiY29tbWVudFRpbWUiLCJzZXRDb21tZW50VGltZSIsIm1lbWJlcnMiLCJzZXRNZW1iZXJzIiwiY29udGVudCIsInNldENvbnRlbnQiLCJyZXNjdWVEYXRhIiwic2V0UmVzY3VlRGF0YSIsIm1pc3Npb25EYXRhIiwic2V0TWlzc2lvbkRhdGEiLCJjb21tZW50RGF0YSIsInNldENvbW1lbnREYXRhIiwidGltZWxpbmUiLCJzZXRUaW1lbGluZSIsImRlbGV0ZUNvbW1lbnRzRGlhbG9nIiwic2V0RGVsZXRlQ29tbWVudHNEaWFsb2ciLCJhbGxWZXNzZWxDcmV3cyIsInNldEFsbFZlc3NlbENyZXdzIiwidmVzc2VsIiwic2V0VmVzc2VsIiwibWFzdGVySUQiLCJzZXRNYXN0ZXJJRCIsImNyZXdNZW1iZXJPcHRpb25zIiwic2V0Q3Jld01lbWJlck9wdGlvbnMiLCJhbGxNZW1iZXJzIiwic2V0QWxsTWVtYmVycyIsImNyZXdNZW1iZXJzIiwic2V0Q3Jld01lbWJlcnMiLCJjcmV3TWVtYmVyc0xpc3QiLCJzZXRDcmV3TWVtYmVyc0xpc3QiLCJsb2dib29rIiwic2V0TG9nYm9vayIsImN1cnJlbnRMb2NhdGlvbiIsInNldEN1cnJlbnRMb2NhdGlvbiIsImxhdGl0dWRlIiwibG9uZ2l0dWRlIiwiY3VycmVudE1pc3Npb25Mb2NhdGlvbiIsInNldEN1cnJlbnRNaXNzaW9uTG9jYXRpb24iLCJzaG93SW5wdXREZXRhaWxzUCIsInNldFNob3dJbnB1dERldGFpbHNQYW5lbCIsImlzV2lkZSIsIm1lbWJlck1vZGVsIiwidmVzc2VsUmVzY3VlTW9kZWwiLCJjZ0V2ZW50TWlzc2lvbk1vZGVsIiwibWlzc2lvblRpbWVsaW5lTW9kZWwiLCJzZWFMb2dzTWVtYmVyTW9kZWwiLCJjbWxic01vZGVsIiwibG9nYm9va01vZGVsIiwiaGFuZGxlVGltZUNoYW5nZSIsImRhdGUiLCJ2ZXNzZWxJRCIsImdldCIsImxvZ2VudHJ5SUQiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJnZXRDdXJyZW50RXZlbnQiLCJoYW5kbGVMb2NhdGlvbkNoYW5nZSIsInZhbHVlIiwiZ2VvTG9jYXRpb25JRCIsInByZXYiLCJldmVudCIsImdldEJ5SWQiLCJ2ZXNzZWxOYW1lIiwiY2FsbFNpZ24iLCJwb2IiLCJ2ZXNzZWxMZW5ndGgiLCJ2ZXNzZWxUeXBlIiwibWFrZUFuZE1vZGVsIiwiY29sb3IiLCJvd25lck5hbWUiLCJwaG9uZSIsImVtYWlsIiwiYWRkcmVzcyIsIm93bmVyT25Cb2FyZCIsImNnTWVtYmVyc2hpcCIsImxvY2F0aW9uSUQiLCJ2ZXNzZWxMb2NhdGlvbklEIiwibWlzc2lvbklEIiwibWlzc2lvbiIsImlkIiwib3BlcmF0aW9uVHlwZSIsImZpbHRlciIsIm9wZXJhdGlvbiIsInNwbGl0IiwiaW5jbHVkZXMiLCJvcGVyYXRpb25EZXNjcmlwdGlvbiIsInZlc3NlbFR5cGVEZXNjcmlwdGlvbiIsImNvbXBsZXRlZEF0IiwibWlzc2lvblR5cGUiLCJyZXBsYWNlQWxsIiwiZGVzY3JpcHRpb24iLCJvcGVyYXRpb25PdXRjb21lIiwiY3VycmVudExvY2F0aW9uSUQiLCJsYXQiLCJsb25nIiwibWlzc2lvblRpbWVsaW5lIiwibm9kZXMiLCJnZXRUcmlwRXZlbnQiLCJ2YXJpYWJsZXMiLCJmZXRjaFBvbGljeSIsIm9uQ29tcGxldGVkIiwicmVzcG9uc2UiLCJyZWFkT25lRXZlbnRUeXBlX1Zlc3NlbFJlc2N1ZSIsIm9uRXJyb3IiLCJlcnJvciIsImNvbnNvbGUiLCJoYW5kbGVTZXRNZW1iZXJMaXN0IiwibWVtYmVyIiwiYXJjaGl2ZWQiLCJmaXJzdE5hbWUiLCJtYXAiLCJsYWJlbCIsInN1cm5hbWUiLCJoYW5kbGVDb21tZW50VGltZUNoYW5nZSIsImxvY2F0aW9uIiwidGl0bGUiLCJ2ZXNzZWxUeXBlcyIsIm1pc3Npb25zIiwib3BlcmF0aW9uT3V0Y29tZXMiLCJjb21tZW50VHlwZXMiLCJoYW5kbGVTYXZlQ29tbWVudHMiLCJ1bmRlZmluZWQiLCJpbnB1dCIsImNvbW1lbnRUeXBlIiwiYXV0aG9ySUQiLCJ2ZXNzZWxSZXNjdWVJRCIsInNhdmUiLCJ1cGRhdGVNaXNzaW9uVGltZWxpbmUiLCJjcmVhdGVNaXNzaW9uVGltZWxpbmUiLCJoYW5kbGVFZGl0b3JDaGFuZ2UiLCJuZXdDb250ZW50IiwiaGFuZGxlU2F2ZSIsInRvU3RyaW5nIiwiY2dNZW1iZXJzaGlwVHlwZSIsImpvaW4iLCJkYXRhIiwiZXZlbnRJRCIsImV2ZW50VHlwZSIsInVwZGF0ZUV2ZW50VHlwZV9WZXNzZWxSZXNjdWUiLCJjcmVhdGVFdmVudFR5cGVfVmVzc2VsUmVzY3VlIiwiY3JlYXRlQ0dFdmVudE1pc3Npb24iLCJ1cGRhdGVDR0V2ZW50TWlzc2lvbiIsImhhbmRsZU1pc3Npb25Mb2NhdGlvbkNoYW5nZSIsImhhbmRsZUNyZWF0ZUNvbW1lbnQiLCJoYW5kbGVFZGl0Q29tbWVudCIsImNvbW1lbnQiLCJoYW5kbGVEZWxldGVDb21tZW50IiwiY29tbWVudElkIiwiZmluZCIsImMiLCJoYW5kbGVEZWxldGVDb21tZW50cyIsIm9mZmxpbmVHZXRTZWFMb2dzTWVtYmVyc0xpc3QiLCJnZXRBbGwiLCJnZXRTZWN0aW9uQ3Jld01lbWJlcnNfTG9nQm9va0VudHJ5U2VjdGlvbiIsInJlYWRDcmV3TWVtYmVyc19Mb2dCb29rRW50cnlTZWN0aW9ucyIsImhhbmRsZVNldExvZ2Jvb2siLCJzZWN0aW9uVHlwZXMiLCJBcnJheSIsImZyb20iLCJTZXQiLCJsb2dCb29rRW50cnlTZWN0aW9ucyIsInNlYyIsImNsYXNzTmFtZSIsImlkcyIsImZvckVhY2giLCJzZWN0aW9uIiwiZ2V0QnlJZHMiLCJzZWFyY2hGaWx0ZXIiLCJpbiIsImdldExvZ0Jvb2tFbnRyeUJ5SUQiLCJxdWVyeUxvZ0Jvb2tFbnRyeSIsImxvZ2Jvb2tFbnRyeUlkIiwicmVhZE9uZUxvZ0Jvb2tFbnRyeSIsInF1ZXJ5VmVzc2VsQ3Jld3MiLCJyZWFkU2VhTG9nc01lbWJlcnMiLCJpdGVtIiwidHJpbSIsInByb2ZpbGUiLCJhdmF0YXIiLCJwcm9maWxlSW1hZ2UiLCJpc0FycmF5Iiwic29tZSIsImNyZXdNZW1iZXIiLCJwdW5jaE91dCIsIm1lbWJlck9wdGlvbnMiLCJsb2FkVmVzc2VsQ3Jld3MiLCJnZXRCeVZlc3NlbElkIiwibWFzdGVyIiwiY3Jld1dpdGhUcmFpbmluZyIsInZlaGljbGVzIiwiZXEiLCJpc0FyY2hpdmVkIiwiZGl2Iiwib3B0aW9ucyIsIm91dGNvbWUiLCJvbkNoYW5nZSIsInBsYWNlaG9sZGVyIiwicm93cyIsImRvY3VtZW50IiwiZ2V0RWxlbWVudEJ5SWQiLCJyZWNvcmQiLCJvbkVkaXQiLCJvbkRlbGV0ZSIsIm9uQ2xpY2siLCJhc0NoaWxkIiwiZGlzYWJsZWQiLCJoaWRkZW4iLCJ2YXJpYW50IiwiaWNvbkxlZnQiLCJvcGVuRGlhbG9nIiwic2V0T3BlbkRpYWxvZyIsInNpemUiLCJoYW5kbGVDcmVhdGUiLCJhY3Rpb25UZXh0IiwiaHRtbEZvciIsInRpbWVJRCIsImZpZWxkTmFtZSIsImF1dGhvciIsIm9wZW4iLCJvbk9wZW5DaGFuZ2UiLCJzaWRlIiwiZSIsInRhcmdldCIsIm1pbiIsImxlZnRDb250ZW50IiwiY2hlY2tlZCIsImlzUmFkaW9TdHlsZSIsIm9uQ2hlY2tlZENoYW5nZSIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue-fields.tsx\n"));

/***/ })

});