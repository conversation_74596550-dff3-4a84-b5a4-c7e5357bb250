'use client'
import React, { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { getSupplier, isOverDueTask } from '@/app/lib/actions'
import { useLazyQuery } from '@apollo/client'
import Loading from '@/app/loading'
import Link from 'next/link'
import { isEmpty, trim } from 'lodash'
import { useRouter, usePathname, useSearchParams } from 'next/navigation'
import { DataTable, createColumns } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { ListHeader } from '@/components/ui/list-header'
import { SealogsInventoryIcon } from '@/app/lib/icons/SealogsInventoryIcon'
import { InventoryFilterActions } from '@/components/filter/components/inventory-actions'
import { Badge, H4, P } from '@/components/ui'
import { cn } from '@/app/lib/utils'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'
import { ReadInventories } from './queries'

// Component to display categories with overflow handling
const CategoryDisplay = ({
    categories,
    className,
    limits = { small: 1, 'tablet-md': 1, landscape: 2, laptop: 3, desktop: 4 },
}: {
    categories: any
    className?: string
    limits?: Record<string, number>
}) => {
    const bp = useBreakpoints()

    if (!categories?.nodes || categories.nodes.length === 0) {
        return null
    }

    // Get current limit based on active breakpoints (from largest to smallest)
    const currentLimit = (() => {
        if (bp.desktop && limits.desktop !== undefined) return limits.desktop
        if (bp.laptop && limits.laptop !== undefined) return limits.laptop
        if (bp.landscape && limits.landscape !== undefined)
            return limits.landscape
        if (bp['tablet-md'] && limits['tablet-md'] !== undefined)
            return limits['tablet-md']
        return limits.small ?? 1
    })()

    const visibleCategories = categories.nodes?.slice(0, currentLimit)
    const remainingCount = categories.nodes?.length - currentLimit

    return (
        <div className="flex items-center">
            <span className={cn('text-sm', className)}>
                {visibleCategories?.map((cat: any) => cat.name).join(', ')}
                {remainingCount > 0 && (
                    <>
                        {visibleCategories?.length > 0 && ', '}
                        <Popover>
                            <PopoverTrigger asChild>
                                <Button
                                    variant="link"
                                    className="p-0 h-auto text-sm">
                                    +{remainingCount} more
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-80">
                                <div className="space-y-2">
                                    <P className="font-medium text-sm">
                                        All Categories
                                    </P>
                                    <P className="text-sm">
                                        {categories.nodes
                                            .map((cat: any) => cat.name)
                                            .join(', ')}
                                    </P>
                                </div>
                            </PopoverContent>
                        </Popover>
                    </>
                )}
            </span>
        </div>
    )
}

// Component to display maintenance status badge
const MaintenanceStatusBadge = ({ inventory }: { inventory: any }) => {
    // Calculate maintenance status from componentMaintenanceChecks
    const getMaintenanceStatus = (inventory: any) => {
        const checks = inventory.componentMaintenanceChecks?.nodes || []

        if (checks.length === 0) {
            return null
        }

        // Filter active tasks (not archived)
        const activeTasks = checks.filter((task: any) => !task?.archived)

        // Count overdue tasks using the same logic as inventory view
        const overdueTasks = activeTasks.filter((task: any) => {
            const overDueInfo = isOverDueTask(task)
            const isOverdue = overDueInfo.status === 'High'

            return isOverdue
        })

        if (overdueTasks.length > 0) {
            return { type: 'overdue', count: overdueTasks.length }
        }

        // If there are maintenance checks but none are overdue, show good status
        return { type: 'good' }
    }

    const maintenanceStatus = getMaintenanceStatus(inventory) || {
        type: 'good',
    }

    return (
        <>
            {maintenanceStatus?.type === 'overdue' ? (
                <Badge variant="destructive">{maintenanceStatus.count}</Badge>
            ) : maintenanceStatus?.type === 'good' ? (
                <Badge variant="success">
                    <svg
                        className={`h-5 w-5`}
                        viewBox="0 0 20 20"
                        fill="#27AB83"
                        aria-hidden="true">
                        <path
                            fillRule="evenodd"
                            d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                            clipRule="evenodd"
                        />
                    </svg>
                </Badge>
            ) : null}
        </>
    )
}

export default function InventoryList() {
    const router = useRouter()
    const pathname = usePathname()
    const searchParams = useSearchParams()

    const [inventories, setInventories] = useState<any[]>([])
    const [suppliers, setSuppliers] = useState<any>(null)
    const [filter, setFilter] = useState({} as SearchFilter)
    const [keywordFilter, setKeywordFilter] = useState<any[]>([])
    const [isLoading, setIsLoading] = useState(true)
    const [page, setPage] = useState(1)
    const limit = 100

    // Query inventories via GraphQL.
    const [queryInventories, { loading: queryInventoriesLoading }] =
        useLazyQuery(ReadInventories, {
            fetchPolicy: 'cache-and-network',
            onError: (error: any) => {
                console.error('queryInventories error', error)
            },
        })

    // Load supplier data.
    getSupplier(setSuppliers)

    // Function to load all pages of inventories.
    const loadInventories = async (
        searchFilter: SearchFilter = {},
        searchkeywordFilter: any[] = keywordFilter,
    ) => {
        if (searchkeywordFilter.length > 0) {
            // For keyword filters, we need to load all pages for each keyword filter
            const allInventories: any[] = []

            for (const keywordFilter of searchkeywordFilter) {
                let offset = 0
                let hasNextPage = true

                while (hasNextPage) {
                    const response = await queryInventories({
                        variables: {
                            filter: { ...searchFilter, ...keywordFilter },
                            offset: offset,
                            limit: limit,
                        },
                    })

                    if (response.data?.readInventories?.nodes) {
                        allInventories.push(
                            ...response.data.readInventories.nodes,
                        )
                        hasNextPage =
                            response.data.readInventories.pageInfo.hasNextPage
                        offset += limit
                    } else {
                        hasNextPage = false
                    }
                }
            }

            // Remove duplicates based on ID
            const uniqueInventories = allInventories.filter(
                (value: any, index: number, self: any) =>
                    self.findIndex((v: any) => v.id === value.id) === index,
            )

            setInventories(uniqueInventories)
        } else {
            // Load all pages for regular filter
            const allInventories: any[] = []
            let offset = 0
            let hasNextPage = true
            let totalCount = 0
            while (hasNextPage) {
                const response = await queryInventories({
                    variables: {
                        filter: searchFilter,
                        offset: offset,
                        limit: limit,
                    },
                })

                if (response.data?.readInventories?.nodes) {
                    allInventories.push(...response.data.readInventories.nodes)
                    hasNextPage =
                        response.data.readInventories.pageInfo.hasNextPage
                    totalCount =
                        response.data.readInventories.pageInfo.totalCount
                    offset += limit
                } else {
                    hasNextPage = false
                }
            }

            setInventories(allInventories)
        }
    }

    // Called when the Filter component changes.
    const handleFilterOnChange = ({ type, data }: any) => {
        const searchFilter: SearchFilter = { ...filter }
        if (type === 'vessel') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.vesselID = { in: data.map((item) => +item.value) }
            } else if (data && !Array.isArray(data)) {
                searchFilter.vesselID = { eq: +data.value }
            } else {
                delete searchFilter.vesselID
            }
        }
        if (type === 'supplier') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.suppliers = {
                    id: { in: data.map((item) => +item.value) },
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.suppliers = { id: { in: [+data.value] } }
            } else {
                delete searchFilter.suppliers
            }
        }
        if (type === 'category') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.categories = {
                    id: { eq: data.map((item) => String(item.value)) },
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.categories = { id: { eq: String(data.value) } }
            } else {
                delete searchFilter.categories
            }
        }
        if (type === 'keyword') {
            if (!isEmpty(trim(data?.value))) {
                setKeywordFilter([
                    { item: { contains: data.value } },
                    { title: { contains: data.value } },
                    { productCode: { contains: data.value } },
                    { description: { contains: data.value } },
                    { comments: { contains: data.value } },
                ])
            } else {
                setKeywordFilter([])
            }
        }
        setFilter(searchFilter)
        setPage(1)
        loadInventories(
            searchFilter,
            type === 'keyword'
                ? !isEmpty(trim(data?.value))
                    ? [
                          { item: { contains: data.value } },
                          { title: { contains: data.value } },
                          { productCode: { contains: data.value } },
                          { description: { contains: data.value } },
                          { comments: { contains: data.value } },
                      ]
                    : []
                : keywordFilter,
        )
    }

    useEffect(() => {
        setPage(1)
        loadInventories(filter, keywordFilter)
        setIsLoading(false)
    }, [filter, page])

    useEffect(() => {
        loadInventories(filter, keywordFilter)
    }, [filter, keywordFilter])

    const columns = createColumns([
        {
            accessorKey: 'title',
            header: 'Item',
            cellClassName: 'phablet:w-auto w-full',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return (
                    <div className="w-full py-2.5 space-y-1.5">
                        <div className="flex justify-between items-center">
                            <div>
                                <Link
                                    href={`/inventory/view/?id=${inventory.id}&redirect_to=${pathname}?${searchParams.toString()}&tab=inventory`}
                                    className="flex items-center">
                                    {inventory.quantity +
                                        ' x ' +
                                        inventory.item}
                                </Link>
                                <div className="text-curious-blue-400 uppercase laptop:hidden text-[10px]">
                                    {inventory.vessel?.title ||
                                        inventory.location ||
                                        'N/A'}
                                </div>
                            </div>
                            <div className="phablet:hidden">
                                <MaintenanceStatusBadge inventory={inventory} />
                            </div>
                        </div>

                        {inventory.suppliers?.nodes.length > 0 && (
                            <span className="flex tablet-sm:hidden items-center gap-2">
                                <P>Sup:</P>
                                {inventory.suppliers?.nodes?.map(
                                    (supplier: any) => (
                                        <Link
                                            key={String(supplier.id)}
                                            href={`/inventory/suppliers/view?id=${supplier.id}`}>
                                            {supplier.name}
                                        </Link>
                                    ),
                                )}
                            </span>
                        )}
                        {inventory.categories?.nodes.length > 0 && (
                            <span className="flex tablet-lg:hidden items-center gap-1.5">
                                <P>Cat:</P>
                                <CategoryDisplay
                                    className="py-1 h-fit text-sm"
                                    categories={inventory.categories}
                                    limits={{ small: 2 }}
                                />
                            </span>
                        )}
                    </div>
                )
            },
            filterFn: (row: any, columnId: string, filterValue: string) => {
                const inventory = row.original
                const text = (inventory.item || '').toLowerCase()
                return text.includes(filterValue.toLowerCase())
            },
        },
        {
            accessorKey: 'location',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Location" />
            ),
            breakpoint: 'laptop',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return (
                    <div>
                        {inventory.vessel?.title || inventory.location || 'N/A'}
                    </div>
                )
            },
            cellAlignment: 'left',
            filterFn: (row: any, columnId: string, filterValue: string) => {
                const inventory = row.original
                const loc = (
                    inventory.vessel?.title ||
                    inventory.location ||
                    ''
                ).toLowerCase()
                return loc.includes(filterValue.toLowerCase())
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA =
                    rowA?.original?.vessel?.title ||
                    rowA?.original?.location ||
                    ''
                const valueB =
                    rowB?.original?.vessel?.title ||
                    rowB?.original?.location ||
                    ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'maintenance',
            header: 'Maintenance',
            breakpoint: 'phablet',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return <MaintenanceStatusBadge inventory={inventory} />
            },
        },
        {
            accessorKey: 'categories',
            header: 'Categories',
            cellAlignment: 'left',
            breakpoint: 'tablet-lg',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return (
                    <CategoryDisplay
                        categories={inventory.categories}
                        limits={{
                            small: 1,
                            landscape: 1,
                            laptop: 2,
                            desktop: 3,
                        }}
                    />
                )
            },
        },
        {
            accessorKey: 'suppliers',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Suppliers" />
            ),
            cellAlignment: 'right',
            breakpoint: 'tablet-sm',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return (
                    <>
                        {inventory.suppliers?.nodes?.map((supplier: any) => (
                            <Link
                                key={String(supplier.id)}
                                href={`/inventory/suppliers/view?id=${supplier.id}`}>
                                {supplier.name}
                            </Link>
                        ))}
                    </>
                )
            },
            filterFn: (row: any, columnId: string, filterValue: string) => {
                const inventory = row.original
                if (!filterValue) return true
                const supplierNames = (inventory.suppliers?.nodes || [])
                    .map((s: any) => s.name.toLowerCase())
                    .join(' ')
                return supplierNames.includes(filterValue.toLowerCase())
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.suppliers?.nodes?.[0]?.name || ''
                const valueB = rowB?.original?.suppliers?.nodes?.[0]?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
    ])

    return (
        <>
            <ListHeader
                icon={<SealogsInventoryIcon className="size-12" />}
                title="All inventory"
                actions={<InventoryFilterActions />}
            />
            <div className="mt-16">
                <DataTable
                    columns={columns}
                    data={inventories}
                    showToolbar={true}
                    pageSize={50}
                    isLoading={isLoading || queryInventoriesLoading}
                    onChange={handleFilterOnChange}
                />
            </div>
        </>
    )
}
